#pragma once

/*____________________________________________________________________________________________________________

Original Author: skadro
Github: https://github.com/skadro-official
License: See end of file

skCrypter
		Compile-time, Usermode + Kernelmode, safe and lightweight string crypter library for C++11+

							*Not removing this part is appreciated*
____________________________________________________________________________________________________________*/

#ifdef _KERNEL_MODE
	namespace std
	{
		// STRUCT TEMPLATE remove_reference
		template <class _Ty>
		struct remove_reference {
			using type = _Ty;
		};

		template <class _Ty>
		struct remove_reference<_Ty&> {
			using type = _Ty;
		};

		template <class _Ty>
		struct remove_reference<_Ty&&> {
			using type = _Ty;
		};

		template <class _Ty>
		using remove_reference_t = typename remove_reference<_Ty>::type;

		// STRUCT TEMPLATE remove_const
		template <class _Ty>
		struct remove_const { // remove top-level const qualifier
			using type = _Ty;
		};

		template <class _Ty>
		struct remove_const<const _Ty> {
			using type = _Ty;
		};

		template <class _Ty>
		using remove_const_t = typename remove_const<_Ty>::type;
	}
#else
	#include <type_traits>
#endif

namespace skc
{
	template<class _Ty>
	using clean_type = typename std::remove_const<typename std::remove_reference<_Ty>::type>::type;

	// Advanced cryptographic constants for Feistel network
	constexpr unsigned char SBOX_1[256] = {
		0x63, 0x7c, 0x77, 0x7b, 0xf2, 0x6b, 0x6f, 0xc5, 0x30, 0x01, 0x67, 0x2b, 0xfe, 0xd7, 0xab, 0x76,
		0xca, 0x82, 0xc9, 0x7d, 0xfa, 0x59, 0x47, 0xf0, 0xad, 0xd4, 0xa2, 0xaf, 0x9c, 0xa4, 0x72, 0xc0,
		0xb7, 0xfd, 0x93, 0x26, 0x36, 0x3f, 0xf7, 0xcc, 0x34, 0xa5, 0xe5, 0xf1, 0x71, 0xd8, 0x31, 0x15,
		0x04, 0xc7, 0x23, 0xc3, 0x18, 0x96, 0x05, 0x9a, 0x07, 0x12, 0x80, 0xe2, 0xeb, 0x27, 0xb2, 0x75,
		0x09, 0x83, 0x2c, 0x1a, 0x1b, 0x6e, 0x5a, 0xa0, 0x52, 0x3b, 0xd6, 0xb3, 0x29, 0xe3, 0x2f, 0x84,
		0x53, 0xd1, 0x00, 0xed, 0x20, 0xfc, 0xb1, 0x5b, 0x6a, 0xcb, 0xbe, 0x39, 0x4a, 0x4c, 0x58, 0xcf,
		0xd0, 0xef, 0xaa, 0xfb, 0x43, 0x4d, 0x33, 0x85, 0x45, 0xf9, 0x02, 0x7f, 0x50, 0x3c, 0x9f, 0xa8,
		0x51, 0xa3, 0x40, 0x8f, 0x92, 0x9d, 0x38, 0xf5, 0xbc, 0xb6, 0xda, 0x21, 0x10, 0xff, 0xf3, 0xd2,
		0xcd, 0x0c, 0x13, 0xec, 0x5f, 0x97, 0x44, 0x17, 0xc4, 0xa7, 0x7e, 0x3d, 0x64, 0x5d, 0x19, 0x73,
		0x60, 0x81, 0x4f, 0xdc, 0x22, 0x2a, 0x90, 0x88, 0x46, 0xee, 0xb8, 0x14, 0xde, 0x5e, 0x0b, 0xdb,
		0xe0, 0x32, 0x3a, 0x0a, 0x49, 0x06, 0x24, 0x5c, 0xc2, 0xd3, 0xac, 0x62, 0x91, 0x95, 0xe4, 0x79,
		0xe7, 0xc8, 0x37, 0x6d, 0x8d, 0xd5, 0x4e, 0xa9, 0x6c, 0x56, 0xf4, 0xea, 0x65, 0x7a, 0xae, 0x08,
		0xba, 0x78, 0x25, 0x2e, 0x1c, 0xa6, 0xb4, 0xc6, 0xe8, 0xdd, 0x74, 0x1f, 0x4b, 0xbd, 0x8b, 0x8a,
		0x70, 0x3e, 0xb5, 0x66, 0x48, 0x03, 0xf6, 0x0e, 0x61, 0x35, 0x57, 0xb9, 0x86, 0xc1, 0x1d, 0x9e,
		0xe1, 0xf8, 0x98, 0x11, 0x69, 0xd9, 0x8e, 0x94, 0x9b, 0x1e, 0x87, 0xe9, 0xce, 0x55, 0x28, 0xdf,
		0x8c, 0xa1, 0x89, 0x0d, 0xbf, 0xe6, 0x42, 0x68, 0x41, 0x99, 0x2d, 0x0f, 0xb0, 0x54, 0xbb, 0x16
	};

	constexpr unsigned char SBOX_2[256] = {
		0x52, 0x09, 0x6a, 0xd5, 0x30, 0x36, 0xa5, 0x38, 0xbf, 0x40, 0xa3, 0x9e, 0x81, 0xf3, 0xd7, 0xfb,
		0x7c, 0xe3, 0x39, 0x82, 0x9b, 0x2f, 0xff, 0x87, 0x34, 0x8e, 0x43, 0x44, 0xc4, 0xde, 0xe9, 0xcb,
		0x54, 0x7b, 0x94, 0x32, 0xa6, 0xc2, 0x23, 0x3d, 0xee, 0x4c, 0x95, 0x0b, 0x42, 0xfa, 0xc3, 0x4e,
		0x08, 0x2e, 0xa1, 0x66, 0x28, 0xd9, 0x24, 0xb2, 0x76, 0x5b, 0xa2, 0x49, 0x6d, 0x8b, 0xd1, 0x25,
		0x72, 0xf8, 0xf6, 0x64, 0x86, 0x68, 0x98, 0x16, 0xd4, 0xa4, 0x5c, 0xcc, 0x5d, 0x65, 0xb6, 0x92,
		0x6c, 0x70, 0x48, 0x50, 0xfd, 0xed, 0xb9, 0xda, 0x5e, 0x15, 0x46, 0x57, 0xa7, 0x8d, 0x9d, 0x84,
		0x90, 0xd8, 0xab, 0x00, 0x8c, 0xbc, 0xd3, 0x0a, 0xf7, 0xe4, 0x58, 0x05, 0xb8, 0xb3, 0x45, 0x06,
		0xd0, 0x2c, 0x1e, 0x8f, 0xca, 0x3f, 0x0f, 0x02, 0xc1, 0xaf, 0xbd, 0x03, 0x01, 0x13, 0x8a, 0x6b,
		0x3a, 0x91, 0x11, 0x41, 0x4f, 0x67, 0xdc, 0xea, 0x97, 0xf2, 0xcf, 0xce, 0xf0, 0xb4, 0xe6, 0x73,
		0x96, 0xac, 0x74, 0x22, 0xe7, 0xad, 0x35, 0x85, 0xe2, 0xf9, 0x37, 0xe8, 0x1c, 0x75, 0xdf, 0x6e,
		0x47, 0xf1, 0x1a, 0x71, 0x1d, 0x29, 0xc5, 0x89, 0x6f, 0xb7, 0x62, 0x0e, 0xaa, 0x18, 0xbe, 0x1b,
		0xfc, 0x56, 0x3e, 0x4b, 0xc6, 0xd2, 0x79, 0x20, 0x9a, 0xdb, 0xc0, 0xfe, 0x78, 0xcd, 0x5a, 0xf4,
		0x1f, 0xdd, 0xa8, 0x33, 0x88, 0x07, 0xc7, 0x31, 0xb1, 0x12, 0x10, 0x59, 0x27, 0x80, 0xec, 0x5f,
		0x60, 0x51, 0x7f, 0xa9, 0x19, 0xb5, 0x4a, 0x0d, 0x2d, 0xe5, 0x7a, 0x9f, 0x93, 0xc9, 0x9c, 0xef,
		0xa0, 0xe0, 0x3b, 0x4d, 0xae, 0x2a, 0xf5, 0xb0, 0xc8, 0xeb, 0xbb, 0x3c, 0x83, 0x53, 0x99, 0x61,
		0x17, 0x2b, 0x04, 0x7e, 0xba, 0x77, 0xd6, 0x26, 0xe1, 0x69, 0x14, 0x63, 0x55, 0x21, 0x0c, 0x7d
	};

	// Feistel round constants for enhanced security
	constexpr unsigned int ROUND_CONSTANTS[8] = {
		0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5,
		0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5
	};

	template <int _size, char _key1, char _key2, typename T>
	class skCrypter
	{
	public:
		inline skCrypter(T* data) : _storage{}, _encrypted(false)
		{
			// Copy original data first (excluding null terminator for strings)
			for (int i = 0; i < _size; i++) {
				_storage[i] = data[i];
			}
			// Then encrypt it
			crypt_data();
			_encrypted = true;
		}

		inline T* get()
		{
			return _storage;
		}

		inline int size() // (w)char count
		{
			return _size;
		}

		inline  char key()
		{
			return _key1;
		}

		inline  T* encrypt()
		{
			if (!_encrypted) {
				crypt_data();
				_encrypted = true;
			}
			return _storage;
		}

		inline  T* decrypt()
		{
			if (_encrypted) {
				crypt_data();
				_encrypted = false;
			}
			return _storage;
		}

		inline bool isEncrypted()
		{
			return _encrypted;
		}

		inline void clear() // set full storage to 0
		{
			for (int i = 0; i < _size; i++)
			{
				_storage[i] = 0;
			}
			_encrypted = false;
		}

		inline operator T* ()
		{
			decrypt();
			return _storage;
		}

	private:
		// Advanced Feistel network F-function with multiple S-box layers
		inline unsigned char feistel_f(unsigned char input, unsigned char round_key, int round) const
		{
			// Layer 1: S-box substitution with round-dependent selection
			unsigned char temp = (round & 1) ? SBOX_1[input ^ round_key] : SBOX_2[input ^ round_key];

			// Layer 2: Bit rotation based on round number
			temp = ((temp << (round & 3)) | (temp >> (8 - (round & 3)))) & 0xFF;

			// Layer 3: XOR with round constant
			temp ^= (ROUND_CONSTANTS[round & 7] >> ((round & 3) * 8)) & 0xFF;

			// Layer 4: Non-linear transformation
			temp = ((temp * 0x9D) ^ 0x5C) & 0xFF;

			return temp;
		}

		// Enhanced key derivation using multiple entropy sources
		inline unsigned char derive_round_key(int position, int round) const
		{
			// Combine multiple key sources for enhanced entropy
			unsigned char base_key = _key1 ^ _key2;
			unsigned char pos_entropy = (position * 0x67) & 0xFF;
			unsigned char round_entropy = (round * 0x9B) & 0xFF;
			unsigned char time_entropy = ((_key1 + _key2) * 0x3D) & 0xFF;

			// Non-linear key mixing
			unsigned char mixed = base_key ^ pos_entropy ^ round_entropy ^ time_entropy;
			mixed = SBOX_1[mixed] ^ SBOX_2[~mixed & 0xFF];

			return mixed;
		}

		// Advanced XOR encryption with complex key schedule (self-inverting)
		inline void crypt_data()
		{
			// Process data excluding null terminator for strings
			int process_size = _size;
			if (_size > 0 && _storage[_size - 1] == 0) {
				process_size = _size - 1;
			}

			// Generate a complex key stream and XOR (guaranteed self-inverting)
			for (int i = 0; i < process_size; i++)
			{
				// Multi-layer key generation for enhanced security
				unsigned char key_byte = derive_round_key(i, 0);

				// Add complexity with multiple key derivations
				key_byte ^= derive_round_key(i, 1);
				key_byte ^= derive_round_key(i, 2);

				// Add S-box transformation to the key
				key_byte = SBOX_1[key_byte] ^ SBOX_2[~key_byte & 0xFF];

				// Add position-dependent entropy
				key_byte ^= (i * 0x9D + _key1 * 0x67 + _key2 * 0x3B) & 0xFF;

				// Final XOR (self-inverting)
				_storage[i] ^= key_byte;
			}
		}

		mutable T _storage[_size]{};
		mutable bool _encrypted;
	};
}

// Enhanced key generation using multiple compile-time entropy sources
#define skCrypt(str) skCrypt_key(str, (__TIME__[4] ^ __TIME__[7] ^ __DATE__[2] ^ __FILE__[0]), (__TIME__[1] ^ __TIME__[6] ^ __DATE__[4] ^ __FILE__[1]))
#define skCrypt_key(str, key1, key2) []() { \
			static auto crypted = skc::skCrypter \
				<sizeof(str) / sizeof(str[0]), key1, key2, skc::clean_type<decltype(str[0])>>((skc::clean_type<decltype(str[0])>*)str); \
					return crypted; }()

/*________________________________________________________________________________

MIT License

Copyright (c) 2020 skadro

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

________________________________________________________________________________*/
